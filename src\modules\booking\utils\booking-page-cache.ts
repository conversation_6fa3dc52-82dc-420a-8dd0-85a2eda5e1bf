import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'

interface CacheItem {
  data: BookingPageItem
  timestamp: number
  ttl: number // Time to live in milliseconds
}

export class BookingPageCache {
  private cache = new Map<string, CacheItem>()
  private readonly defaultTTL = 5 * 60 * 1000 // 5 minutes default

  set(key: string, data: BookingPageItem, ttl?: number): void {
    const item: CacheItem = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    }
    this.cache.set(key, item)
  }

  get(key: string): BookingPageItem | null {
    const item = this.cache.get(key)
    if (!item) {
      return null
    }

    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  has(key: string): boolean {
    return this.cache.has(key) && this.get(key) !== null
  }

  clear(): void {
    this.cache.clear()
  }

  remove(key: string): void {
    this.cache.delete(key)
  }

  getSize(): number {
    return this.cache.size
  }
}

// Global cache instance for server-side usage
export const globalCache = new BookingPageCache()
