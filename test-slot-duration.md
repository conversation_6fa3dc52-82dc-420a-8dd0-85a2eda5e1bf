# Test Slot Duration Configuration

## Summary of Changes

I have successfully implemented slot duration configuration for the OperatingHoursConfig component and updated all related components to use the configurable slot duration instead of hardcoded values.

### Changes Made:

1. **Updated BookingConfig Interface** (`src/modules/admin/create-booking-page-v2/types/index.ts`)
   - Added `slotDuration: number` property to the BookingConfig interface

2. **Updated Store** (`src/modules/admin/create-booking-page-v2/stores/create-booking-page-v2.store.ts`)
   - Added `slotDuration: 60` to the initial booking config

3. **Enhanced OperatingHoursConfig Component** (`src/modules/admin/create-booking-page-v2/components/templates/configs/shared/OperatingHoursConfig.tsx`)
   - Added slot duration options: 15, 30, 45, 60 minutes
   - Added Select dropdown for choosing slot duration
   - Updated UI to show both operating hours and slot duration configuration
   - Added handler for slot duration changes

4. **Updated Slot Generation Components**:
   - **GridSlotSection.tsx**: Updated to use `slotDuration` from store instead of hardcoded 30 minutes
   - **TableGridSlotSection.tsx**: Updated to use `slotDuration` from store instead of hardcoded 30 minutes

5. **Updated Template Configurations** (`src/modules/admin/create-booking-page-v2/stores/store.helper.ts`)
   - Added appropriate slot durations for each template:
     - Football: 60 minutes
     - Tennis: 60 minutes  
     - Badminton: 60 minutes
     - Basketball: 60 minutes
     - Conference: 60 minutes
     - Restaurant: 120 minutes (2 hours)

### Features:

- **Configurable Slot Duration**: Users can now select from 15, 30, 45, or 60-minute time slots
- **Live Preview**: Changes to slot duration immediately affect the slot generation in preview
- **Template-Specific Defaults**: Each template has appropriate default slot durations
- **Consistent UI**: The configuration is integrated into the existing OperatingHoursConfig component

### How to Test:

1. Navigate to the booking page creation flow
2. Go to the Operating Hours configuration step
3. You should see a new "Thời gian mỗi slot" dropdown with options: 15 phút, 30 phút, 45 phút, 60 phút
4. Change the slot duration and observe that the preview updates accordingly
5. The time slots in the booking grid should reflect the selected duration

### Technical Details:

- The slot duration is stored in the Zustand store and persists across the configuration flow
- All slot generation functions now use `addMinutes(currentSlot, slotDuration)` instead of hardcoded values
- The configuration is applied consistently across both grid and table layout templates
- Template switching preserves the slot duration setting unless overridden by template defaults

The implementation follows the existing patterns in the codebase and maintains compatibility with all existing functionality while adding the requested slot duration configuration feature.
