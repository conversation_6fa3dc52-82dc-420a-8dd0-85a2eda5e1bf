# Shared Config Components

<PERSON><PERSON><PERSON> mục này chứa các config components có thể tái sử dụng cho các template khác nhau.

## Cấu trúc

```
shared/
├── BasicInfoConfig.tsx          # Cấu hình thông tin cơ bản (banner, title, subtitle, image, colors)
├── OperatingHoursConfig.tsx     # Cấu hình giờ hoạt động
├── FieldsConfig.tsx             # Cấu hình danh sách sân
├── DescriptionLocationConfig.tsx # Cấu hình mô tả và địa điểm
├── ContactInfoConfig.tsx        # Cấu hình thông tin liên hệ
├── index.ts                     # Export tất cả components
└── README.md                    # Tài liệu này
```

## Cách sử dụng

### 1. Import các components

```tsx
import { 
  BasicInfoConfig, 
  OperatingHoursConfig, 
  FieldsConfig, 
  DescriptionLocationConfig, 
  ContactInfoConfig 
} from './shared'
```

### 2. Sử dụng trong template config

```tsx
const YourTemplateConfig: React.FC = React.memo(() => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6 text-orange-500" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Cấu hình template của bạn
          </h3>
          <p className="text-sm text-gray-600">
            Thiết lập thông tin cơ bản cho trang đặt sân
          </p>
        </div>
      </div>

      {/* Các components tự động lấy data từ store */}
      <BasicInfoConfig />
      <OperatingHoursConfig />
      <FieldsConfig />
      <DescriptionLocationConfig />
      <ContactInfoConfig />
    </div>
  )
})
```

## Components chi tiết

### BasicInfoConfig
- **Props**: Không cần props (tự động lấy từ store)
- **Chức năng**: Cấu hình tiêu đề, mô tả, ảnh banner, màu nền, cách hiển thị ảnh
- **Features**: Debounced input, image upload, color picker

### OperatingHoursConfig
- **Props**: Không cần props (tự động lấy từ store)
- **Chức năng**: Cấu hình giờ mở cửa và đóng cửa

### FieldsConfig
- **Props**: Không cần props (tự động lấy từ store)
- **Chức năng**: Quản lý danh sách sân (thêm, xóa, sửa)
- **Features**: Debounced field name input, field type selection

### DescriptionLocationConfig
- **Props**: Không cần props (tự động lấy từ store)
- **Chức năng**: Cấu hình mô tả chi tiết và địa điểm

### ContactInfoConfig
- **Props**: Không cần props (tự động lấy từ store)
- **Chức năng**: Cấu hình thông tin liên hệ và mạng xã hội

## Lợi ích

1. **Tái sử dụng code**: Các config blocks có thể dùng cho nhiều template
2. **Dễ bảo trì**: Chỉ cần sửa ở một nơi, tất cả template đều được cập nhật
3. **Consistent UI**: Đảm bảo giao diện nhất quán giữa các template
4. **Performance**: Sử dụng React.memo và debounced input để tối ưu hiệu suất
5. **No Prop Drilling**: Components tự động lấy data từ store, không cần truyền props
6. **Type Safety**: Có type definitions rõ ràng
7. **Đơn giản hóa**: Template config chỉ cần import và sử dụng, không cần logic phức tạp

## Hooks hỗ trợ

### useDebounce
- **Location**: `../hooks/useDebounce.ts`
- **Chức năng**: Debounce input để tránh re-render quá nhiều
- **Usage**: `const debouncedValue = useDebounce(value, 300)`

## Mở rộng

Để thêm config component mới:

1. Tạo file component trong thư mục `shared/`
2. Export component trong `index.ts`
3. Sử dụng trong các template config
4. Cập nhật README này

## Ví dụ template hoàn chỉnh

Xem `ModernSportConfigV2.tsx` và `ClassicSportConfig.tsx` để biết cách sử dụng đầy đủ các shared components.

# BasicInfoConfig Component

## Tính năng Upload Ảnh

Component này đã được cập nhật để hỗ trợ upload ảnh thông qua API `generateUploadUrl`.

### Cách hoạt động

1. **Chọn file**: Người dùng click vào nút "Chọn ảnh" để chọn file ảnh
2. **Gọi API generateUploadUrl**: Component gọi API để lấy upload URL và public URL
3. **Upload file**: File được upload trực tiếp lên storage service (S3, etc.)
4. **Cập nhật UI**: Sau khi upload thành công, ảnh được hiển thị và lưu vào store

### Components được sử dụng

- **useImageUpload hook**: Xử lý logic upload với progress tracking
- **UploadProgressBar**: Hiển thị tiến trình upload và trạng thái
- **BasicInfoConfig**: Component chính với UI upload

### API Endpoint

```typescript
POST /agent/upload/generate-url
```

**Payload:**
```typescript
{
  originalName: string
  mimeType: string
  category: string
}
```

**Response:**
```typescript
{
  uploadUrl: string
  fileKey: string
  publicUrl: string
  expiresIn: number
  instructions: string
}
```

### Tính năng

- ✅ Progress tracking với progress bar
- ✅ Error handling và hiển thị lỗi
- ✅ Loading states
- ✅ Preview ảnh sau khi upload
- ✅ Xóa ảnh
- ✅ Responsive design

### Sử dụng

```tsx
import { BasicInfoConfig } from './BasicInfoConfig'

// Trong component cha
<BasicInfoConfig />
```

### Dependencies

- `@/components/ui/button`
- `@/components/ui/card`
- `@/components/ui/input`
- `@/components/ui/label`
- `@/components/ui/select`
- `@/components/ui/progress`
- `lucide-react`
- Custom hooks: `useImageUpload`, `useDebounce`
