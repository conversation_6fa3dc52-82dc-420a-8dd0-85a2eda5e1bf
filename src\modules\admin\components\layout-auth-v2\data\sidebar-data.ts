import type { SidebarData } from '../types'
import { appPaths } from '@/utils/app-routes'
import { AudioWaveform, CalendarCog, Command, GalleryVerticalEnd, IdCardIcon } from 'lucide-react'

export const sidebarData = (pickslotPageId: string): SidebarData => {
  return {
    user: {
      name: 'satnaing',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
    },
    teams: [
      {
        name: 'Shadcn Admin',
        logo: Command,
        plan: 'Vite + ShadcnUI',
      },
      {
        name: 'Acme Inc',
        logo: GalleryVerticalEnd,
        plan: 'Enterprise',
      },
      {
        name: 'Acme Corp.',
        logo: AudioWaveform,
        plan: 'Startup',
      },
    ],
    navGroups: [
      {
        title: 'Quản lý chung',
        items: [
          {
            title: 'Dashboard',
            url: appPaths.admin.pagePanel(pickslotPageId),
            icon: IdCardIcon,
          },
          {
            title: 'Design',
            url: appPaths.admin.pageEdit(pickslotPageId),
            icon: CalendarCog,
          },
        ],
      },
    ],
  }
}
