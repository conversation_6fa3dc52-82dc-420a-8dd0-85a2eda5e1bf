import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { bookingAPIs } from '../booking.apis'
import { globalCache } from '../utils/booking-page-cache'

interface FetchBookingPageOptions {
  slug: string
  forceRefresh?: boolean
  ttl?: number
  isPreview?: boolean
}

export class BookingPageService {
  private static instance: BookingPageService

  private constructor() {}

  static getInstance(): BookingPageService {
    if (!BookingPageService.instance) {
      BookingPageService.instance = new BookingPageService()
    }
    return BookingPageService.instance
  }

  async fetchBookingPage({ slug, forceRefresh = false, ttl, isPreview = false }: FetchBookingPageOptions): Promise<BookingPageItem | null> {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedData = globalCache.get(slug)
        if (cachedData) {
          console.warn(`[Cache] Using cached data for slug: ${slug}`)
          return cachedData
        }
      }

      // Fetch from API
      console.warn(`[Cache] Fetching fresh data for slug: ${slug}, isPreview: ${isPreview}`)
      const response = await bookingAPIs.getBookingPageBySlug(slug, { isPreview })

      if (response?.status?.success && response?.data) {
        const bookingPage = response.data

        // Cache the data
        globalCache.set(slug, bookingPage, ttl)
        console.warn(`[Cache] Cached data for slug: ${slug}`)

        return bookingPage
      }

      return null
    } catch (error) {
      console.error(`[Cache] Error fetching booking page for slug ${slug}:`, error)

      // Try to return cached data as fallback if available
      const cachedData = globalCache.get(slug)
      if (cachedData) {
        console.warn(`[Cache] Using cached data as fallback for slug: ${slug}`)
        return cachedData
      }

      throw error
    }
  }

  async fetchBookingPageWithMetadata(slug: string, isPreview = false): Promise<{
    bookingPage: BookingPageItem | null
    metadata: {
      title: string
      description?: string
    }
  }> {
    try {
      const bookingPage = await this.fetchBookingPage({ slug, isPreview, ttl: 60 })

      if (bookingPage) {
        return {
          bookingPage,
          metadata: {
            title: `${bookingPage.name} - PickSlot`,
            description: bookingPage.description,
          },
        }
      }

      // Fallback metadata
      return {
        bookingPage: null,
        metadata: {
          title: `${slug} - PickSlot`,
          description: 'Booking page not found',
        },
      }
    } catch (error) {
      console.error(`[Cache] Error in fetchBookingPageWithMetadata for slug ${slug}:`, error)

      // Return fallback metadata
      return {
        bookingPage: null,
        metadata: {
          title: `${slug} - PickSlot`,
          description: 'Error loading booking page',
        },
      }
    }
  }

  // Method to invalidate cache for a specific slug
  invalidateCache(slug: string): void {
    globalCache.remove(slug)
    console.warn(`[Cache] Invalidated cache for slug: ${slug}`)
  }

  // Method to clear all cache
  clearAllCache(): void {
    globalCache.clear()
    console.warn('[Cache] Cleared all cache')
  }

  // Method to get cache stats
  getCacheStats(): { size: number } {
    return {
      size: globalCache.getSize(),
    }
  }
}

export const bookingPageService = BookingPageService.getInstance()
