'use client'

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { useNavigation } from '@/hooks/useNavigation'
import { BookingPageCard } from '@/modules/admin/components/BookingPageCard'
import BookingPageCardSkeleton from '@/modules/admin/manage-booking-pages/components/BookingPageCardSkeleton'
import BookingPagePagination from '@/modules/admin/manage-booking-pages/components/BookingPagePagination'
import EmptyState from '@/modules/admin/manage-booking-pages/components/EmptyState'
import { useBookingPages } from '@/modules/admin/manage-booking-pages/hooks/useBookingPages'
import { appPaths } from '@/utils/app-routes'
import { AlertCircle, RefreshCw } from 'lucide-react'
import React from 'react'

export const BookingPageGrid = () => {
  const {
    bookingPages,
    isLoading,
    error,
    currentPage,
    setCurrentPage,
    totalPages,
    fetchBookingPages,
    hasFilters,
  } = useBookingPages()
  const { navigate } = useNavigation()

  const handleCreateNew = () => {
    navigate(appPaths.admin.createBookingPage())
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <BookingPageCardSkeleton />
        <BookingPageCardSkeleton />
        <BookingPageCardSkeleton />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Lỗi</AlertTitle>
        <AlertDescription>
          {error}
          <Button
            variant="outline"
            size="sm"
            onClick={fetchBookingPages}
            className="mt-2 ml-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Thử lại
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  if (bookingPages.length === 0) {
    return <EmptyState hasFilters={hasFilters} onCreateNew={handleCreateNew} />
  }

  // Các action mặc định
  const handleEditAction = (id: string) => {
    navigate(appPaths.admin.editBookingPage(id))
  }
  const handleViewStatsAction = (_: string) => {}
  const handleViewLiveAction = (_: string, slug: string) => {
    window.open(`/${slug}`, '_blank')
  }
  const handleOpenControlPanel = (id: string) => {
    navigate(appPaths.admin.pagePanel(id))
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
        {bookingPages.map(page => (
          <BookingPageCard
            key={page._id}
            item={{
              _id: page._id,
              name: page.name,
              status: page.status,
              template: page.templateCode || '',
              templateCode: page.templateCode,
              createdAt: page.createdAt,
              description: page.description,
              avatar: `https://placehold.co/100x100?text=${encodeURIComponent(page.name?.[0] || 'B')}`,
              views: 0,
              bookings: 0,
              slug: page.slug,
            }}
            onEditAction={handleEditAction}
            onViewStatsAction={handleViewStatsAction}
            onViewLiveAction={handleViewLiveAction}
            onOpenControlPanel={handleOpenControlPanel}
          />
        ))}
      </div>
      <div className="mt-6">
        <BookingPagePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </div>
    </>
  )
}

export default BookingPageGrid
