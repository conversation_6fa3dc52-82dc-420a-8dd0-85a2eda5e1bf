'use client'

import type { BookingFormData, BookingFormSectionProps } from './types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/libs/utils'
import { formatDateYMDHMS } from '@/utils/time'
import { zodResolver } from '@hookform/resolvers/zod'
import React, { useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useSelectedSlotsStore } from '../../../stores/selected-slots.store'

// Form validation schema
const bookingFormSchema = z.object({
  customerName: z
    .string()
    .min(1, 'Vui lòng nhập họ và tên')
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
    .max(50, 'Họ và tên không được quá 50 ký tự')
    .trim(),
  customerEmail: z
    .string()
    .min(1, '<PERSON><PERSON> lòng nhập email')
    .email('<PERSON>ail không hợp lệ')
    .max(100, 'Email không được quá 100 ký tự')
    .trim(),
  customerPhone: z
    .string()
    .min(1, 'Vui lòng nhập số điện thoại')
    .regex(/^[0-9+\-\s()]+$/, 'Số điện thoại không hợp lệ')
    .min(10, 'Số điện thoại phải có ít nhất 10 số')
    .max(15, 'Số điện thoại không được quá 15 số')
    .trim(),
  notes: z
    .string()
    .max(500, 'Ghi chú không được quá 500 ký tự')
    .optional(),
})

type FormValues = z.infer<typeof bookingFormSchema>

// Memoized Input Field Component
const MemoizedInputField = React.memo<{
  type?: string
  label: string
  placeholder: string
  register: any
  error?: string
  required?: boolean
  className?: string
}>(({ type = 'text', label, placeholder, register, error, required = false, className }) => (
  <div className="w-full">
    <label className="block text-sm font-medium mb-1.5 text-gray-700">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
    <Input
      type={type}
      className={cn(
        'w-full p-3 border rounded-md transition-colors',
        error ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500',
        className,
      )}
      placeholder={placeholder}
      {...register}
    />
    {error && (
      <p className="mt-1 text-sm text-red-500">{error}</p>
    )}
  </div>
))

MemoizedInputField.displayName = 'MemoizedInputField'

// Memoized Textarea Field Component
const MemoizedTextareaField = React.memo<{
  label: string
  placeholder: string
  register: any
  error?: string
  required?: boolean
  className?: string
}>(({ label, placeholder, register, error, required = false, className }) => (
  <div className="w-full">
    <label className="block text-sm font-medium mb-1.5 text-gray-700">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
    <Textarea
      className={cn(
        'w-full p-3 border rounded-md transition-colors resize-none',
        error ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500',
        className,
      )}
      placeholder={placeholder}
      rows={4}
      {...register}
    />
    {error && (
      <p className="mt-1 text-sm text-red-500">{error}</p>
    )}
  </div>
))

MemoizedTextareaField.displayName = 'MemoizedTextareaField'

/**
 * BookingFormSection Component
 *
 * Displays a booking form with customer information fields
 */
const BookingFormSection: React.FC<BookingFormSectionProps> = ({
  config,
  previewMode = 'desktop',
  className,
  onSubmit,
  isSubmitting = false,
  showNotes = true,
}) => {
  const isMobile = previewMode === 'mobile'

  // Use store to get selected slots
  const { hasSelectedSlots, getFieldSlots, selectedDate } = useSelectedSlotsStore()

  // Initialize React Hook Form
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(bookingFormSchema),
    mode: 'onBlur',
    defaultValues: {
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      notes: '',
    },
  })

  // Handle form submission
  const handleFormSubmit = useCallback((data: FormValues) => {
    if (onSubmit) {
      onSubmit(data as BookingFormData)
    }
    // Reset form after successful submission in preview mode
    if (!onSubmit) {
      reset()
    }
  }, [onSubmit, reset])

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 shadow-sm',
      isMobile ? 'p-4' : 'p-6',
      className,
    )}
    >
      {/* Section Header */}
      <div className="mb-6">
        <h2 className={cn(
          'font-bold text-gray-900 mb-2',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Thông tin đặt chỗ
        </h2>
        <p className={cn(
          'text-gray-600',
          isMobile ? 'text-sm' : 'text-base',
        )}
        >
          Vui lòng điền đầy đủ thông tin để hoàn tất đặt chỗ
        </p>
      </div>

      {/* Selected Slots Display */}
      {config.fields && hasSelectedSlots() && (
        <div className="mb-4 p-3 bg-orange-50 rounded-lg">
          <div className={cn('font-medium text-orange-700', isMobile ? 'text-xs' : 'text-sm')}>
            <div className="mb-2">
              Ngày đặt:
              {' '}
              <span className="font-semibold">
                {formatDateYMDHMS(selectedDate)}
              </span>

            </div>
            Các khung giờ đã chọn:
          </div>
          <ul className="mt-1 space-y-1">
            {config.fields.map((field) => {
              const fieldSlots = getFieldSlots(field.id)
              return fieldSlots.length > 0
                ? (
                    <li key={field.id} className="flex items-start gap-2">
                      <span className="font-semibold">
                        {field.name}
                        :
                      </span>
                      <span>{fieldSlots.join(', ')}</span>
                    </li>
                  )
                : null
            })}
          </ul>
        </div>
      )}

      {/* Booking Form */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* Customer Name */}
        <MemoizedInputField
          label="Họ và tên"
          placeholder="Nhập họ và tên của bạn"
          register={register('customerName')}
          error={errors.customerName?.message}
          required
        />

        {/* Customer Email */}
        <MemoizedInputField
          type="email"
          label="Email"
          placeholder="Nhập địa chỉ email của bạn"
          register={register('customerEmail')}
          error={errors.customerEmail?.message}
          required
        />

        {/* Customer Phone */}
        <MemoizedInputField
          type="tel"
          label="Số điện thoại"
          placeholder="Nhập số điện thoại của bạn"
          register={register('customerPhone')}
          error={errors.customerPhone?.message}
          required
        />

        {/* Notes (Optional) */}
        {showNotes && (
          <MemoizedTextareaField
            label="Ghi chú"
            placeholder="Nhập ghi chú thêm (không bắt buộc)"
            register={register('notes')}
            error={errors.notes?.message}
          />
        )}

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className={cn(
              'w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors',
              isSubmitting && 'opacity-50 cursor-not-allowed',
              isMobile ? 'text-sm' : 'text-base',
            )}
          >
            {isSubmitting ? 'Đang xử lý...' : 'Đặt chỗ ngay'}
          </Button>
        </div>
      </form>
    </div>
  )
}

export default BookingFormSection
