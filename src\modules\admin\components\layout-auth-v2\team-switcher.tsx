'use client'

import type { BookingPageItem } from '../../apis/booking-page.api'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { useNavigation } from '@/hooks/useNavigation'
import { appPaths } from '@/utils/app-routes'
import { ChevronsUpDown, Command, Plus } from 'lucide-react'
import Link from 'next/link'
import React, { useEffect } from 'react'
import { useBookingPages, useBookingPagesLoading, useFetchBookingPages } from '../../pickslot-page/stores/booking-pages-list.store'

interface Props {
  bookingPageItem: BookingPageItem
}

export function TeamSwitcher({ bookingPageItem }: Props) {
  const { isMobile } = useSidebar()
  const bookingPages = useBookingPages()
  const isLoading = useBookingPagesLoading()
  const fetchBookingPages = useFetchBookingPages()
  const { navigate } = useNavigation()

  const handleSelectPage = (page: BookingPageItem) => {
    navigate(appPaths.admin.pagePanel(page._id))
  }

  useEffect(() => {
    fetchBookingPages()
  }, [fetchBookingPages])

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <Command />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {bookingPageItem?.name}
                </span>
                <span className="truncate text-xs">{bookingPageItem?.plan ?? 'Free'}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Pickslot Page
            </DropdownMenuLabel>

            {isLoading
              ? (
                  <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
                )
              : (
                  bookingPages.map(page => (
                    <DropdownMenuItem
                      key={page._id}
                      onClick={() => handleSelectPage(page)}
                      className="gap-2 p-2"
                    >
                      <div className="flex size-6 items-center justify-center rounded-sm border">
                        <Command className="size-4 shrink-0" />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium">{page.name}</span>
                        <span className="text-xs text-muted-foreground">
                          /
                          {page.slug}
                        </span>
                      </div>
                    </DropdownMenuItem>
                  ))
                )}

            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="bg-background flex size-6 items-center justify-center rounded-md border">
                <Plus className="size-4" />
              </div>
              <Link href={appPaths.admin.createBookingPage()} className="text-muted-foreground font-medium">Add Pickslot Page</Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
