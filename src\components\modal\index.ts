// Export modal API for imperative usage
export {
  closeAllModals,
  closeModal,
  modalAPI,
  modalManagerRef,
  showAlert,
  showConfirm,
  showDeleteConfirm,
  showError,
  showLoading,
  showSuccess,
  showWarning,
  updateModal,
} from './modal-api'
// Export all modal-related components and hooks
export { ModalManager } from './modal-manager'
export type { ModalManagerRef } from './modal-manager'
export { ModalProvider } from './modal-provider'
export { useModalStore } from './modal-store'

export type {
  AlertModalData,
  BaseModalData,
  ConfirmModalData,
  LoadingModalData,
  ModalData,
} from './modal-store'

export { AlertModal } from './modals/alert-modal'

export { ConfirmModal } from './modals/confirm-modal'
// Export individual modal components
export { LoadingModal } from './modals/loading-modal'
export { useModal } from './use-modal'
