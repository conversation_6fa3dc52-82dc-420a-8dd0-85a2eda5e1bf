'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { useNavigation } from '@/hooks/useNavigation'
import { appPaths } from '@/utils/app-routes'
import { ArrowLeft } from 'lucide-react'
import React from 'react'
import { projectMenuConfig, supportMenuItems } from './menu-config'

const ProjectSidebar: React.FC = () => {
  const { navigate } = useNavigation()
  const { state } = useSidebar()

  const handleBackToDashboard = () => {
    navigate(appPaths.admin.dashboard())
  }

  const handleMenuClick = (url: string) => {
    navigate(url)
  }

  return (
    <Sidebar
      variant="sidebar"
      collapsible="icon"
      className="border-r border-gray-200"
    >
      <SidebarHeader className="border-b border-gray-200">
        <div className="p-4">
          <Button
            onClick={handleBackToDashboard}
            variant="ghost"
            size="sm"
            className="w-full justify-start text-gray-600 hover:text-orange-600 hover:bg-orange-50"
          >
            <ArrowLeft className="h-4 w-4" />
            {state === 'expanded' && <span className="ml-2">Back to Dashboard</span>}
          </Button>
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Dynamic Menu Sections */}
        {projectMenuConfig.map(section => (
          <SidebarGroup key={section.label}>
            <SidebarGroupLabel className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
              {section.label}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {section.items.map(item => (
                  <SidebarMenuItem key={item.label}>
                    <SidebarMenuButton
                      onClick={() => handleMenuClick(item.url)}
                      isActive={item.isActive}
                      tooltip={item.description || item.label}
                      className={
                        item.isActive
                          ? 'bg-orange-500 text-white hover:bg-orange-600'
                          : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
                      }
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.label}</span>
                      {item.badge && (
                        <Badge
                          variant="secondary"
                          className="ml-auto h-5 w-5 shrink-0 items-center justify-center rounded-full bg-orange-100 text-orange-600 text-xs"
                        >
                          {item.badge}
                        </Badge>
                      )}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-200">
        <SidebarMenu>
          {supportMenuItems.map(item => (
            <SidebarMenuItem key={item.label}>
              <SidebarMenuButton
                onClick={() => handleMenuClick(item.url)}
                tooltip={item.description || item.label}
                className="text-gray-600 hover:text-orange-600 hover:bg-orange-50"
              >
                <item.icon className="h-4 w-4" />
                <span>{item.label}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}

export default ProjectSidebar
