/* eslint-disable no-unused-vars */
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Types for booked slots
export interface BookedSlot {
  field: string
  time: string
  date: string
  bookingId: string
  status: 'pending' | 'confirmed' | 'cancelled'
}

export interface SelectedSlotsState {
  selectedSlots: Record<string, string[]>
  selectedDate: Date
  bookedSlots: BookedSlot[]
  isLoadingBookedSlots: boolean

  // Actions
  setSelectedSlots: (fieldId: string, timeSlots: string[]) => void
  addSlot: (fieldId: string, timeSlot: string) => void
  removeSlot: (fieldId: string, timeSlot: string) => void
  toggleSlot: (fieldId: string, timeSlot: string) => void
  clearFieldSlots: (fieldId: string) => void
  clearAllSlots: () => void
  setSelectedDate: (date: Date) => void

  // Booked slots actions
  setBookedSlots: (bookedSlots: BookedSlot[]) => void
  setLoadingBookedSlots: (loading: boolean) => void
  clearBookedSlots: () => void

  // Getters
  getFieldSlots: (fieldId: string) => string[]
  hasSelectedSlots: () => boolean
  getTotalSelectedSlots: () => number
  isSlotBooked: (fieldId: string, timeSlot: string) => boolean
  getSlotStatus: (fieldId: string, timeSlot: string) => 'available' | 'selected' | 'pending' | 'confirmed' | 'cancelled'
}

// Store
export const useSelectedSlotsStore = create<SelectedSlotsState>()(
  devtools(
    (set, get) => ({
      // State
      selectedSlots: {},
      selectedDate: new Date(),
      bookedSlots: [],
      isLoadingBookedSlots: false,

      // Actions
      setSelectedSlots: (fieldId: string, timeSlots: string[]) => {
        set(
          state => ({
            selectedSlots: {
              ...state.selectedSlots,
              [fieldId]: timeSlots,
            },
          }),
          false,
          'setSelectedSlots',
        )
      },

      addSlot: (fieldId: string, timeSlot: string) => {
        set(
          (state) => {
            const currentSlots = state.selectedSlots[fieldId] || []
            if (!currentSlots.includes(timeSlot)) {
              return {
                selectedSlots: {
                  ...state.selectedSlots,
                  [fieldId]: [...currentSlots, timeSlot],
                },
              }
            }
            return state
          },
          false,
          'addSlot',
        )
      },

      removeSlot: (fieldId: string, timeSlot: string) => {
        set(
          (state) => {
            const currentSlots = state.selectedSlots[fieldId] || []
            const updatedSlots = currentSlots.filter(slot => slot !== timeSlot)

            if (updatedSlots.length === 0) {
              // Remove field if no slots left
              const { [fieldId]: removed, ...remainingSlots } = state.selectedSlots
              return { selectedSlots: remainingSlots }
            }

            return {
              selectedSlots: {
                ...state.selectedSlots,
                [fieldId]: updatedSlots,
              },
            }
          },
          false,
          'removeSlot',
        )
      },

      toggleSlot: (fieldId: string, timeSlot: string) => {
        const { selectedSlots } = get()
        const currentSlots = selectedSlots[fieldId] || []
        const isSelected = currentSlots.includes(timeSlot)

        if (isSelected) {
          get().removeSlot(fieldId, timeSlot)
        } else {
          get().addSlot(fieldId, timeSlot)
        }
      },

      clearFieldSlots: (fieldId: string) => {
        set(
          (state) => {
            const { [fieldId]: removed, ...remainingSlots } = state.selectedSlots
            return { selectedSlots: remainingSlots }
          },
          false,
          'clearFieldSlots',
        )
      },

      clearAllSlots: () => {
        set({ selectedSlots: {} }, false, 'clearAllSlots')
      },

      setSelectedDate: (date: Date) => {
        console.log('Setting selected date:', date)
        set({ selectedDate: date })
      },

      // Booked slots actions
      setBookedSlots: (bookedSlots: BookedSlot[]) => {
        set({ bookedSlots }, false, 'setBookedSlots')
      },

      setLoadingBookedSlots: (loading: boolean) => {
        set({ isLoadingBookedSlots: loading }, false, 'setLoadingBookedSlots')
      },

      clearBookedSlots: () => {
        set({ bookedSlots: [] }, false, 'clearBookedSlots')
      },

      // Getters
      getFieldSlots: (fieldId: string) => {
        return get().selectedSlots[fieldId] || []
      },

      hasSelectedSlots: () => {
        return Object.keys(get().selectedSlots).length > 0
      },

      getTotalSelectedSlots: () => {
        const { selectedSlots } = get()
        return Object.values(selectedSlots).reduce((total, slots) => total + slots.length, 0)
      },

      // Check if a slot is booked
      isSlotBooked: (fieldId: string, timeSlot: string) => {
        const { bookedSlots } = get()
        return bookedSlots.some(slot =>
          slot.field === fieldId
          && slot.time === timeSlot
          && slot.status !== 'cancelled',
        )
      },

      // Get slot status (available, selected, pending, confirmed, cancelled)
      getSlotStatus: (fieldId: string, timeSlot: string) => {
        const { selectedSlots, bookedSlots } = get()

        // Check if slot is booked first
        const bookedSlot = bookedSlots.find(slot =>
          slot.field === fieldId
          && slot.time === timeSlot
          && slot.status !== 'cancelled',
        )

        if (bookedSlot) {
          return bookedSlot.status
        }

        // Check if slot is selected
        const fieldSlots = selectedSlots[fieldId] || []
        if (fieldSlots.includes(timeSlot)) {
          return 'selected'
        }

        return 'available'
      },
    }),
    {
      name: 'selected-slots-store',
    },
  ),
)
