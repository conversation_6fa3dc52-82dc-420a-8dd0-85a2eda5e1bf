'use client'

import { Progress } from '@/components/ui/progress'
import { CheckCircle, Loader2, XCircle } from 'lucide-react'
import React from 'react'

interface UploadProgressBarProps {
  isUploading: boolean
  progress: number
  error: string | null
}

export const UploadProgressBar: React.FC<UploadProgressBarProps> = React.memo(({
  isUploading,
  progress,
  error,
}) => {
  if (!isUploading && progress === 0 && !error) {
    return null
  }

  return (
    <div className="w-full space-y-2">
      <div className="flex items-center gap-2">
        {isUploading && progress < 100 && (
          <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
        )}
        {progress === 100 && !error && (
          <CheckCircle className="w-4 h-4 text-green-500" />
        )}
        {error && (
          <XCircle className="w-4 h-4 text-red-500" />
        )}
        <span className="text-sm font-medium">
          {isUploading && progress < 100 && '<PERSON><PERSON> tải lên...'}
          {progress === 100 && !error && 'Tải lên thành công!'}
          {error && 'Tải lên thất bại'}
        </span>
      </div>

      {isUploading && (
        <Progress value={progress} className="w-full" />
      )}

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
})

UploadProgressBar.displayName = 'UploadProgressBar'
