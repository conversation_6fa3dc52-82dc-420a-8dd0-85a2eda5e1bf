'use client'

import type { ReactNode } from 'react'
import LayoutProvider from '@/components/layouts/layout-provider'

import { SidebarProvider } from '@/components/ui'
import { useSidebarState } from '@/hooks/use-sidebar-state'
import { useFetchUserProfile, useUser } from '@/modules/user/stores/user.store'
import React, { memo } from 'react'
import ProjectSidebar from '../components/layout/ProjectSidebar'
import AdminHeader from '../dashboard/screens/components/AdminHeader'

type ProjectDashboardLayoutProps = {
  children: ReactNode
}

const ProjectDashboardLayout = memo(({ children }: ProjectDashboardLayoutProps) => {
  // Đọc trạng thái sidebar từ cookie
  const [isOpen, setIsOpen] = useSidebarState()
  const user = useUser()
  const fetchProfile = useFetchUserProfile()

  React.useEffect(() => {
    if (!user) {
      fetchProfile()
    }
  }, [user, fetchProfile])

  return (
    <LayoutProvider>
      <div className="min-h-screen bg-white flex flex-col">
        {/* Top Header - Fixed */}
        <header className="h-14 bg-white text-gray-900 flex items-center justify-between py-4 px-6 border-b border-gray-200 fixed top-0 left-0 right-0 z-50 shadow-sm">
          {/* Left side - Logo and Navigation */}
          <AdminHeader />
        </header>

        {/* Main layout with sidebar - Below fixed header */}
        <div className="flex pt-14 h-screen">
          <SidebarProvider
            defaultOpen={isOpen}
            open={isOpen}
            onOpenChange={setIsOpen}
          >
            {/* Sidebar - Fixed */}
            <div className="fixed left-0 top-14 bottom-0 z-40">
              <ProjectSidebar />
            </div>

            {/* Main content area */}
            <div className={`flex-1 flex flex-col transition-all duration-300 ${isOpen ? 'ml-64' : 'ml-16'}`}>
              {/* Scrollable content area */}
              <section className="flex-1 overflow-y-auto bg-gray-50 text-gray-900">
                <div className="p-6">
                  {children}
                </div>
              </section>
            </div>
          </SidebarProvider>
        </div>
      </div>
    </LayoutProvider>

  )
})

ProjectDashboardLayout.displayName = 'ProjectDashboardLayout'

export default ProjectDashboardLayout
