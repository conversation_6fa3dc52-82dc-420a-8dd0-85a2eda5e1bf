'use client'

import type { MapSectionProps } from './types'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Check, Copy, ExternalLink, MapPin, Navigation } from 'lucide-react'
import React, { useState } from 'react'
import { useCreateBookingPageV2Store } from '../../../stores/create-booking-page-v2.store'

const MapSection: React.FC<MapSectionProps> = ({
  className,
}) => {
  const description = useCreateBookingPageV2Store(state => state.bookingConfig?.description)

  const [copied, setCopied] = useState(false)
  const isMobile = false

  // Use config values or fallback to props or defaults
  const mapAddress = description?.location
  const shouldShowDirections = true

  const handleCopyAddress = async () => {
    try {
      await navigator.clipboard.writeText(mapAddress || '')
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy address:', err)
    }
  }

  const handleGetDirections = () => {
    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${mapAddress}`
    window.open(googleMapsUrl, '_blank')
  }

  const handleOpenInMaps = () => {
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${mapAddress}`
    window.open(googleMapsUrl, '_blank')
  }

  return (
    <Card className={cn('border-orange-200', className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className={cn(
            'font-bold text-gray-900',
            isMobile ? 'text-lg' : 'text-xl',
          )}
          >
            Vị trí & Bản đồ
          </h2>
        </div>

        {/* Map Display */}
        <div className="space-y-4">
          <div className="relative overflow-hidden rounded-lg border border-gray-200">
            {/* Map Placeholder */}
            <div
              className={cn(
                'bg-gray-200 bg-cover bg-center relative',
                isMobile ? 'h-48' : 'h-64',
              )}
            >
              {/* Map Overlay */}
              <div className="absolute inset-0 bg-black/10" />

              {/* Center Pin */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-8 h-8 bg-orange-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                  <MapPin className="w-4 h-4 text-white" />
                </div>
              </div>

              {/* Map Controls */}
              <div className="absolute top-4 right-4">
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-white/90 hover:bg-white shadow-md"
                  onClick={handleOpenInMaps}
                >
                  <ExternalLink className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Address & Actions */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-start gap-3 mb-4">
              <MapPin className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className={cn(
                  'font-semibold text-gray-900 mb-1',
                  isMobile ? 'text-sm' : 'text-base',
                )}
                >
                  Địa chỉ
                </h3>
                <p className={cn(
                  'text-gray-600 leading-relaxed',
                  isMobile ? 'text-xs' : 'text-sm',
                )}
                >
                  {mapAddress}
                </p>
              </div>
            </div>

            {/* Quick Actions */}
            {shouldShowDirections && (
              <div className={cn(
                'flex gap-2 mt-4 flex-wrap',
              )}
              >
                <Button
                  onClick={handleGetDirections}
                  className="bg-orange-500 hover:bg-orange-600 text-white flex-1"
                  size={isMobile ? 'sm' : 'default'}
                >
                  <Navigation className="w-4 h-4 mr-2" />
                  Chỉ đường
                </Button>

                <Button
                  onClick={handleCopyAddress}
                  variant="outline"
                  className="border-orange-300 text-orange-600 hover:bg-orange-50 flex-1"
                  size={isMobile ? 'sm' : 'default'}
                >
                  {copied
                    ? (
                        <>
                          <Check className="w-4 h-4 mr-2" />
                          Đã sao chép
                        </>
                      )
                    : (
                        <>
                          <Copy className="w-4 h-4 mr-2" />
                          Sao chép
                        </>
                      )}
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default MapSection
