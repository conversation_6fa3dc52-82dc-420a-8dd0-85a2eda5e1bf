import type { BookingConfig, BookingField, CreateBookingPageV2State, DeepPartial, PageInfo } from '../types'
import merge from 'lodash/merge'
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { clearErrorsForKeys, createNewField, getTemplateConfig, migrateState, removeFieldFromArray, updateFieldInArray, validateCurrentStepData } from './store.helper'

// ===== CONSTANTS =====
const initialPageInfo: PageInfo = {
  name: '',
  description: '',
  slug: '',
}

const initialBookingConfig: BookingConfig = {
  // Banner settings
  banner: {
    title: 'Đặt sân thể thao',
    subtitle: 'Đặt sân nhanh chóng và dễ dàng',
    image: '',
  },

  // Operating hours
  businessHours: {
    start: '06:00',
    end: '22:00',
    daysOfWeek: [1, 2, 3, 4, 5, 6, 0],
  },

  // Fields configuration
  fields: [
    {
      id: 'field-1',
      name: 'Sân 1',
      type: 'football',
      capacity: 1,
    },
    {
      id: 'field-2',
      name: 'Sân 2',
      type: 'football',
      capacity: 1,
    },
    {
      id: 'field-3',
      name: 'Sân 3',
      type: 'football',
      capacity: 1,
    },
  ],

  // Description settings
  description: 'Hệ thống đặt sân thể thao chuyên nghiệp, đảm bảo trải nghiệm tốt nhất cho khách hàng.',
  location: 'Hà Nội, Việt Nam',

  // Contact information
  contactInfo: {
    phone: '',
    email: '',
    address: '',
    socialLinks: {
      facebook: '',
      instagram: '',
      website: '',
    },
  },

  // Pricing configuration
  pricing: {
    basePrice: 300000,
    currency: 'VNĐ',
    priceUnit: 'hour',
    showPricing: true,
  },

  // Display settings
  showCapacity: true,
  showFieldTypes: true,
  showDirections: true,
}

// ===== ZUSTAND STORE =====
export const useCreateBookingPageV2Store = create<CreateBookingPageV2State>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        currentStep: 1,
        pageInfo: initialPageInfo,
        selectedTemplateId: '',
        bookingConfig: initialBookingConfig,
        isLoading: false,
        errors: {},
        shouldPersist: false,

        // Step navigation actions
        setCurrentStep: (step: number) => {
          set({ currentStep: step }, false, 'setCurrentStep')
        },

        nextStep: () => {
          const { currentStep } = get()
          if (currentStep < 3) {
            set({ currentStep: currentStep + 1, errors: {} }, false, 'nextStep')
          }
        },

        prevStep: () => {
          const { currentStep } = get()
          if (currentStep > 1) {
            set({ currentStep: currentStep - 1, errors: {} }, false, 'prevStep')
          }
        },

        // Page info actions
        updatePageInfo: (info: Partial<PageInfo>) => {
          set(
            state => ({
              pageInfo: { ...state.pageInfo, ...info },
              errors: clearErrorsForKeys(state.errors, Object.keys(info)),
            }),
            false,
            'updatePageInfo',
          )
        },

        // Template actions
        setSelectedTemplate: (templateId: string) => {
          set(
            state => ({
              selectedTemplateId: templateId,
              errors: clearErrorsForKeys(state.errors, ['selectedTemplateId']),
            }),
            false,
            'setSelectedTemplate',
          )
          // Auto load template config
          get().loadTemplateConfig(templateId)
        },

        loadTemplateConfig: (templateId: string) => {
          const config = getTemplateConfig(templateId)
          if (config) {
            set(
              state => ({
                bookingConfig: { ...state.bookingConfig, ...config },
                errors: clearErrorsForKeys(state.errors, Object.keys(config)),
              }),
              false,
              'loadTemplateConfig',
            )
          }
        },

        getTemplateConfig: (templateId: string) => {
          return getTemplateConfig(templateId)
        },

        // Booking config actions
        updateBookingConfig: (config: DeepPartial<BookingConfig>) => {
          set(
            (state) => {
              // Merge trực tiếp vào state.bookingConfig, không tạo object mới
              merge(state.bookingConfig, config)
              return {
                bookingConfig: state.bookingConfig,
                errors: clearErrorsForKeys(state.errors, Object.keys(config)),
              }
            },
            false,
            'updateBookingConfig',
          )
        },

        // Field management actions
        addField: () => {
          const { bookingConfig } = get()
          const newField = createNewField(bookingConfig.fields)

          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: [...state.bookingConfig.fields, newField],
              },
              errors: clearErrorsForKeys(state.errors, ['fields']),
            }),
            false,
            'addField',
          )
        },

        removeField: (fieldId: string) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: removeFieldFromArray(state.bookingConfig.fields, fieldId),
              },
            }),
            false,
            'removeField',
          )
        },

        updateField: (fieldId: string, updates: Partial<BookingField>) => {
          set(
            state => ({
              bookingConfig: {
                ...state.bookingConfig,
                fields: updateFieldInArray(state.bookingConfig.fields, fieldId, updates),
              },
            }),
            false,
            'updateField',
          )
        },

        // Validation actions
        validateCurrentStep: () => {
          const { currentStep, pageInfo, selectedTemplateId, bookingConfig } = get()
          return validateCurrentStepData(currentStep, pageInfo, selectedTemplateId, bookingConfig)
        },

        // Reset actions
        reset: () => {
          set(
            {
              currentStep: 1,
              pageInfo: initialPageInfo,
              selectedTemplateId: '',
              bookingConfig: initialBookingConfig,
              isLoading: false,
              errors: {},
            },
            false,
            'reset',
          )
        },

        resetToDefaultConfig: () => {
          set(
            { bookingConfig: initialBookingConfig, errors: {} },
            false,
            'resetToDefaultConfig',
          )
        },

        clearStorage: () => {
          localStorage.removeItem('create-booking-page-v2-store')
          get().reset()
        },

        // Toggle persist storage
        setShouldPersist: (value: boolean) => {
          set({ shouldPersist: value }, false, 'setShouldPersist')
          if (!value) {
            localStorage.removeItem('create-booking-page-v2-store')
          }
        },

        // Initialize data from BookingPageItem (for edit mode)
        initializeFromBookingPage: (bookingPageData, currentStep = 3) => {
          // Extract data from the first block (assuming it contains the config)
          const configBlock = bookingPageData.blocks?.find((block: any) => block.type === 'config')
          const configData = configBlock?.data || {}

          // Map BookingPageItem to CreateBookingPageV2State format
          const pageInfo: PageInfo = {
            name: bookingPageData.name || '',
            description: bookingPageData.description || '',
            slug: bookingPageData.slug || '',
          }

          const selectedTemplateId = bookingPageData.templateCode || ''

          // Convert config data to BookingConfig format
          const bookingConfig = configData as BookingConfig

          set(
            {
              currentStep,
              pageInfo,
              selectedTemplateId,
              bookingConfig,
              isLoading: false,
              errors: {},
            },
            false,
            'initializeFromBookingPage',
          )
        },
      }),
      {
        name: 'create-booking-page-v2-store',
        partialize: (state) => {
          if (!state.shouldPersist) {
            return {}
          }
          return {
            currentStep: state.currentStep,
            pageInfo: state.pageInfo,
            selectedTemplateId: state.selectedTemplateId,
            bookingConfig: state.bookingConfig,
          }
        },
        version: 2,
        migrate: migrateState,
      },
    ),
    {
      // name: 'create-booking-page-v2-store',
    },
  ),
)
