/* eslint-disable no-unused-vars */
import { create } from 'zustand'

export interface BaseModalData {
  id: string
  type: string
  title?: string
  description?: string
  onClose?: () => void
  variant?: string
}

export interface LoadingModalData extends BaseModalData {
  type: 'loading'
  message?: string
  showSpinner?: boolean
  allowClose?: boolean
}

export interface ConfirmModalData extends BaseModalData {
  type: 'confirm'
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
}

export interface AlertModalData extends BaseModalData {
  type: 'alert'
  onOk?: () => void
  okText?: string
  variant?: 'default' | 'destructive' | 'warning' | 'success'
}

export type ModalData = LoadingModalData | ConfirmModalData | AlertModalData

interface ModalStore {
  modals: ModalData[]
  openModal: (modal: Omit<ModalData, 'id'>) => string
  closeModal: (id: string) => void
  closeAllModals: () => void
  updateModal: (id: string, updates: Partial<ModalData>) => void
}

export const useModalStore = create<ModalStore>((set, get) => ({
  modals: [],

  openModal: (modal) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newModal = { ...modal, id }

    set(state => ({
      modals: [...state.modals, newModal] as ModalData[],
    }))

    return id
  },

  closeModal: (id) => {
    const modal = get().modals.find(m => m.id === id)
    if (modal?.onClose) {
      modal.onClose()
    }

    set(state => ({
      modals: state.modals.filter(modal => modal.id !== id),
    }))
  },

  closeAllModals: () => {
    const { modals } = get()
    modals.forEach((modal) => {
      if (modal.onClose) {
        modal.onClose()
      }
    })

    set({ modals: [] })
  },

  updateModal: (id, updates) => {
    set(state => ({
      modals: state.modals.map(modal =>
        modal.id === id ? { ...modal, ...updates } : modal,
      ) as ModalData[],
    }))
  },
}))
