import type { ReactNode } from 'react'
import type { NavCollapsible, NavGroup as NavGroupType, NavItem, NavLink } from './types'
import { Badge } from '@/components/ui'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { ChevronRight, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useState } from 'react'

export function NavGroup({ title, items }: NavGroupType) {
  const { state, isMobile } = useSidebar()
  const pathname = usePathname()
  const href = pathname

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const key = `${item.title}-${item.url}`

          if (!item.items) {
            return <SidebarMenuLink key={key} item={item} href={href} />
          }

          if (state === 'collapsed' && !isMobile) {
            return (
              <SidebarMenuCollapsedDropdown key={key} item={item} href={href} />
            )
          }

          return <SidebarMenuCollapsible key={key} item={item} href={href} />
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}

const NavBadge = ({ children }: { children: ReactNode }) => (
  <Badge className="rounded-full px-1 py-0 text-xs">{children}</Badge>
)

const SidebarMenuLink = ({ item, href }: { item: NavLink, href: string }) => {
  const { setOpenMobile } = useSidebar()
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(false)

  // Check if current path matches the item URL to determine active state
  const isPathActive = pathname === item.url || pathname.startsWith(item.url)

  // If the path is active, clear the loading state
  if (isPathActive && isLoading) {
    setIsLoading(false)
  }

  const handleClick = () => {
    if (!isPathActive) {
      setIsLoading(true)
      setOpenMobile(false)
      router.push(item.url)
    }
  }

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={checkIsActive(href, item)}
        suppressHydrationWarning
        tooltip={item.title}
      >
        <Link href={item.url} onClick={handleClick}>
          {item.icon && (
            <div className="h-6 w-6 flex items-center justify-center">
              {isLoading && !isPathActive && <Loader2 className="animate-spin" />}
              {(!isLoading || isPathActive) && <item.icon />}
            </div>
          )}
          <span>{item.title}</span>
          {item.badge && <NavBadge>{item.badge}</NavBadge>}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}

const SidebarMenuCollapsible = ({
  item,
  href,
}: {
  item: NavCollapsible
  href: string
}) => {
  const { setOpenMobile } = useSidebar()
  const router = useRouter()
  const pathname = usePathname()
  const [clickedItem, setClickedItem] = useState<string | null>(null)

  const handleClick = (url: string) => {
    // Only set loading and navigate if we're not already on this page
    if (!pathname.startsWith(url)) {
      setClickedItem(url)
      setOpenMobile(false)
      router.push(url)
    }
  }

  return (
    <Collapsible
      asChild
      defaultOpen={checkIsActive(href, item, true)}
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton tooltip={item.title}>
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="CollapsibleContent">
          <SidebarMenuSub>
            {item.items.map((subItem) => {
              // Check if current path matches this subitem
              const isPathActive = pathname === subItem.url || pathname.startsWith(subItem.url)
              const isClicked = clickedItem === subItem.url

              // If the path is active, clear the clicked state
              if (isPathActive && clickedItem === subItem.url) {
                setClickedItem(null)
              }

              return (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton
                    asChild
                    isActive={checkIsActive(href, subItem)}
                  >
                    <Link href={subItem.url} onClick={() => handleClick(subItem.url)}>
                      {subItem.icon && (
                        <div className="h-6 w-6 flex items-center justify-center">
                          {isClicked && !isPathActive && <Loader2 className="animate-spin" />}
                          {(!isClicked || isPathActive) && <subItem.icon />}
                        </div>
                      )}
                      <span>{subItem.title}</span>
                      {subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              )
            })}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

const SidebarMenuCollapsedDropdown = ({
  item,
  href,
}: {
  item: NavCollapsible
  href: string
}) => {
  const router = useRouter()
  const pathname = usePathname()
  const [clickedItem, setClickedItem] = useState<string | null>(null)

  const handleClick = (url: string) => {
    if (!pathname.startsWith(url)) {
      setClickedItem(url)
      router.push(url)
    }
  }

  return (
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            tooltip={item.title}
            isActive={checkIsActive(href, item)}
          >
            {item.icon && <item.icon />}
            <span>{item.title}</span>
            {item.badge && <NavBadge>{item.badge}</NavBadge>}
            <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent side="right" align="start" sideOffset={4}>
          <DropdownMenuLabel>
            {item.title}
            {' '}
            {item.badge ? `(${item.badge})` : ''}
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {item.items.map((sub) => {
            // Check if current path matches this sub item
            const isPathActive = pathname === sub.url || pathname.startsWith(sub.url)
            const isClicked = clickedItem === sub.url

            // If the path is active, clear the clicked state
            if (isPathActive && clickedItem === sub.url) {
              setClickedItem(null)
            }

            return (
              <DropdownMenuItem key={`${sub.title}-${sub.url}`} asChild>
                <Link
                  href={sub.url}
                  className={`${checkIsActive(href, sub) ? 'bg-secondary' : ''}`}
                  onClick={() => handleClick(sub.url)}
                >
                  {sub.icon && (
                    <div className="flex items-center justify-center mr-2 h-4 w-4">
                      {isClicked && !isPathActive && <Loader2 className="animate-spin" />}
                      {(!isClicked || isPathActive) && <sub.icon />}
                    </div>
                  )}
                  <span className="max-w-52 text-wrap">{sub.title}</span>
                  {sub.badge && (
                    <span className="ml-auto text-xs">{sub.badge}</span>
                  )}
                </Link>
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  )
}

function checkIsActive(href: string, item: NavItem, mainNav = false) {
  // Remove domain from href if it exists
  const cleanHref = href.replace(/^https?:\/\/[^/]+/, '')

  return (
    cleanHref === item.url // exact match after removing domain
    || href === item.url // exact match with full URL
    || cleanHref.split('?')[0] === item.url // match without query params
    || !!item?.items?.filter(i => i.url === cleanHref).length // child nav is active
    || (mainNav
      && cleanHref.split('/')[1] !== ''
      && cleanHref.split('/')[1] === item?.url?.split('/')[1])
  )
}
