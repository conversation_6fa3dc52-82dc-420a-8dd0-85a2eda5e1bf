import type { NextFetchEvent, NextRequest } from 'next/server'
import createMiddleware from 'next-intl/middleware'
import { NextResponse } from 'next/server'
import { routing } from './libs/i18nNavigation'
import { Admin<PERSON><PERSON><PERSON><PERSON><PERSON>, RouteUtils } from './middleware-config'
import { AppConfig } from './utils/AppConfig'

// Cache for static file paths
const staticFileCache = new Set<string>()

// Internationalization middleware with caching
const intlMiddleware = createMiddleware({
  ...routing,
  localeDetection: true,
  localePrefix: AppConfig.localePrefix,
})

// Main middleware function
export default async function middleware(
  request: NextRequest,
  _event: NextFetchEvent,
): Promise<NextResponse> {
  try {
    const path = request.nextUrl.pathname

    // Quick check for static files using cache
    if (staticFileCache.has(path) || RouteUtils.isStaticFile(path)) {
      if (!staticFileCache.has(path)) {
        staticFileCache.add(path)
      }
      return NextResponse.next()
    }

    // Skip security check for public routes
    if (path.startsWith('/_next') || path.startsWith('/api')) {
      return intlMiddleware(request)
    }

    // Security check with Arcjet only for non-public routes
    // await SecurityService.checkSecurity(request)

    // Handle admin authentication
    const adminResponse = await AdminAuthHandler.handleAdminRoute(request)
    if (adminResponse) {
      return adminResponse
    }

    // Continue with i18n processing
    return intlMiddleware(request)
  } catch (error) {
    console.error('Middleware error:', error)
    return intlMiddleware(request)
  }
}

// Configure which paths the middleware should run on
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
}
