'use client'

import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'
import { Header } from '../../components/layout-auth-v2/header'
import { Main } from '../../components/layout-auth-v2/main'
import { TopNav } from '../../components/layout-auth-v2/top-nav'
import ProfileDropdown from '../../components/ProfileDropdown'
import { BookingConfigStep } from '../components'
import { PageInfoStep } from '../components/steps/PageInfoStep'
import { TemplateSelectionStep } from '../components/steps/TemplateSelectionStep'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

interface EditBookingPageV2ScreenProps {
  bookingPageId: string
}

const EditBookingPageV2Screen: React.FC<EditBookingPageV2ScreenProps> = () => {
  const currentStep = useCreateBookingPageV2Store(state => state.currentStep)

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <PageInfoStep />
      case 2:
        return <TemplateSelectionStep />
      case 3:
        return <BookingConfigStep />
      default:
        return <PageInfoStep />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50/95">
      <Header className="sticky top-0 z-10 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <TopNav links={topNav} />
        <div className="ml-auto flex items-center space-x-4">
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="grid gap-6 sm:gap-8">
          {/* Overview Section */}
          {renderCurrentStep()}
        </div>
      </Main>
    </div>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: 'dashboard',
    isActive: true,
    disabled: true,
  },
]

const EditBookingPageV2ScreenWrapper: React.FC<EditBookingPageV2ScreenProps> = (props) => {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { bookingPageId } = props

  const initializeFromBookingPage = useCreateBookingPageV2Store(state => state.initializeFromBookingPage)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Reset store first to clear any previous data
        useCreateBookingPageV2Store.getState().reset()

        // Fetch booking page data
        const response = await bookingPageAPIs.getBookingPageById(bookingPageId)

        if (response?.status?.success && response?.data) {
          const bookingPageData = response.data

          // Initialize store with fetched data
          initializeFromBookingPage(bookingPageData)

          toast.success('Dữ liệu đã được tải thành công')
        } else {
          setError('Không thể tải dữ liệu booking page')
          toast.error('Không thể tải dữ liệu booking page')
        }
      } catch (error: any) {
        console.error('Error fetching booking page:', error)
        setError('Đã xảy ra lỗi khi tải dữ liệu')
        toast.error('Đã xảy ra lỗi khi tải dữ liệu')
      } finally {
        setIsLoading(false)
      }
    }

    if (bookingPageId) {
      fetchData()
    }
  }, [bookingPageId])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-orange-100/30 to-orange-200/50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-orange-100/30 to-orange-200/50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Không thể tải dữ liệu</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
          >
            Thử lại
          </button>
        </div>
      </div>
    )
  }

  return <EditBookingPageV2Screen {...props} />
}

export default EditBookingPageV2ScreenWrapper
