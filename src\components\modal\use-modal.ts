import type { AlertModalData, ConfirmModalData, LoadingModalData } from './modal-store'
import { useModalStore } from './modal-store'

export function useModal() {
  const { openModal, closeModal, closeAllModals, updateModal } = useModalStore()

  // Loading Modal
  const showLoading = (options?: Partial<Omit<LoadingModalData, 'type' | 'id'>>) => {
    return openModal({
      type: 'loading',
      title: 'Loading...',
      message: 'Please wait while we process your request.',
      showSpinner: true,
      allowClose: false,
      ...options,
    })
  }

  // Confirm Modal
  const showConfirm = (options: Omit<ConfirmModalData, 'type' | 'id'>) => {
    return openModal({
      type: 'confirm',
      title: 'Confirm Action',
      description: 'Are you sure you want to proceed?',
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      variant: 'default',
      ...options,
    })
  }

  // Alert <PERSON>
  const showAlert = (options: Omit<AlertModalData, 'type' | 'id'>) => {
    return openModal({
      type: 'alert',
      title: 'Alert',
      description: 'This is an alert message.',
      okText: 'OK',
      variant: 'default',
      ...options,
    })
  }

  // Success Alert
  const showSuccess = (message: string, title = 'Success') => {
    return showAlert({
      title,
      description: message,
      variant: 'success',
    })
  }

  // Error Alert
  const showError = (message: string, title = 'Error') => {
    return showAlert({
      title,
      description: message,
      variant: 'destructive',
    })
  }

  // Warning Alert
  const showWarning = (message: string, title = 'Warning') => {
    return showAlert({
      title,
      description: message,
      variant: 'warning',
    })
  }

  // Delete Confirmation
  const showDeleteConfirm = (
    onConfirm: () => void | Promise<void>,
    itemName = 'this item',
  ) => {
    return showConfirm({
      title: 'Delete Confirmation',
      description: `Are you sure you want to delete ${itemName}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      variant: 'destructive',
      onConfirm,
    })
  }

  return {
    // Core functions
    openModal,
    closeModal,
    closeAllModals,
    updateModal,

    // Convenience functions
    showLoading,
    showConfirm,
    showAlert,
    showSuccess,
    showError,
    showWarning,
    showDeleteConfirm,
  }
}
