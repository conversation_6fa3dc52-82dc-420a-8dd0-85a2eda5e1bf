'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { useCallback, useRef } from 'react'
import { globalCache } from '../utils/booking-page-cache'

export const useBookingPageCache = () => {
  const cacheRef = useRef(globalCache)

  const getCachedBookingPage = useCallback((slug: string): BookingPageItem | null => {
    return cacheRef.current.get(slug)
  }, [])

  const setCachedBookingPage = useCallback((slug: string, data: BookingPageItem, ttl?: number): void => {
    cacheRef.current.set(slug, data, ttl)
  }, [])

  const hasCachedBookingPage = useCallback((slug: string): boolean => {
    return cacheRef.current.has(slug)
  }, [])

  const clearCache = useCallback((): void => {
    cacheRef.current.clear()
  }, [])

  const removeFromCache = useCallback((slug: string): void => {
    cacheRef.current.remove(slug)
  }, [])

  const getCacheSize = useCallback((): number => {
    return cacheRef.current.getSize()
  }, [])

  return {
    getCachedBookingPage,
    setCachedBookingPage,
    hasCachedBookingPage,
    clearCache,
    removeFromCache,
    getCacheSize,
  }
}

// Re-export globalCache for server-side usage
export { globalCache } from '../utils/booking-page-cache'
