'use client'

import Logo from '@/assets/svgs/Logo'
import ProfileDropdown from '@/modules/admin/components/ProfileDropdown'
import { useFetchUserProfile, useUser } from '@/modules/user/stores/user.store'
import React from 'react'

export const AdminHeader: React.FC = () => {
  const user = useUser()
  const fetchProfile = useFetchUserProfile()

  React.useEffect(() => {
    if (!user) {
      fetchProfile()
    }
  }, [user, fetchProfile])

  return (
    <header className="w-full flex items-center justify-between py-2 px-6 bg-white border-b border-gray-100 shadow-sm">
      <div className="flex items-center gap-3">
        <Logo width={40} height={40} />
        <span className="font-bold text-xl tracking-tight text-primary">PICKSLOT</span>
      </div>
      <ProfileDropdown />
    </header>
  )
}

export default AdminHeader
