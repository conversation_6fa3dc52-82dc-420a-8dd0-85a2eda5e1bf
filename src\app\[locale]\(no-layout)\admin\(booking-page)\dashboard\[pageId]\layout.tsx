import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { AuthenticatedLayout } from '@/modules/admin/components/layout-auth-v2/authenticated-layout'
import { notFound } from 'next/navigation'

export default async function RootLayout(props: {
  children: React.ReactNode
  params: {
    pageId: string
  }
}) {
  const { pageId } = await props.params
  const bookingPageResponse = await bookingPageAPIs.getBookingPageById(pageId)

  if (!bookingPageResponse.data) {
    return notFound()
  }

  return (
    <AuthenticatedLayout bookingPageItem={bookingPageResponse.data!}>
      {props.children}
    </AuthenticatedLayout>
  )
}
