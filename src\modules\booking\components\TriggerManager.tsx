'use client'

import type React from 'react'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2'
import { useSelectedSlotsStore } from '@/modules/admin/create-booking-page-v2/stores/selected-slots.store'
import { formatDateYMD } from '@/utils/time'
import { useEffect, useRef } from 'react'
import { bookingAPIs } from '../booking.apis'

interface TriggerManagerProps {
  bookingPageId: string
}

/**
 * TriggerManager Component
 *
 * Manages various triggers and side effects for the booking page:
 * - Loads booked slots when date changes
 * - Handles other booking-related triggers
 */
export const TriggerManager: React.FC<TriggerManagerProps> = ({ bookingPageId }) => {
  const selectedDate = useSelectedSlotsStore(state => state.selectedDate)
  const setBookedSlots = useSelectedSlotsStore(state => state.setBookedSlots)
  const setLoadingBookedSlots = useSelectedSlotsStore(state => state.setLoadingBookedSlots)
  const clearBookedSlots = useSelectedSlotsStore(state => state.clearBookedSlots)
  const isPreview = useCreateBookingPageV2Store(state => state.isPreview)

  // Ref to track previous date to avoid unnecessary API calls
  const prevDateRef = useRef<string>('')

  // Load booked slots when date changes
  useEffect(() => {
    const loadBookedSlots = async () => {
      if (!bookingPageId || !selectedDate) {
        return
      }

      const formattedDate = formatDateYMD(selectedDate)
      if (!formattedDate) {
        return
      }

      // Skip if date hasn't changed
      if (prevDateRef.current === formattedDate) {
        return
      }

      // Skip loading in preview mode
      if (isPreview) {
        clearBookedSlots()
        return
      }

      try {
        setLoadingBookedSlots(true)
        prevDateRef.current = formattedDate

        const response = await bookingAPIs.getBookedSlots({
          bookingPageId,
          date: formattedDate,
        })

        if (response?.data?.bookedSlots) {
          setBookedSlots(response.data.bookedSlots)
        } else {
          clearBookedSlots()
        }
      } catch (error) {
        console.error('Failed to load booked slots:', error)
        clearBookedSlots()
      } finally {
        setLoadingBookedSlots(false)
      }
    }

    loadBookedSlots()
  }, [
    bookingPageId,
    selectedDate,
    isPreview,
    setBookedSlots,
    setLoadingBookedSlots,
    clearBookedSlots,
  ])

  // Clear booked slots when switching to preview mode
  useEffect(() => {
    if (isPreview) {
      clearBookedSlots()
    }
  }, [isPreview, clearBookedSlots])

  // This component doesn't render anything, it's just for side effects
  return null
}

export default TriggerManager
