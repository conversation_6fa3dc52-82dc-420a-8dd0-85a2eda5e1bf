import { getToken } from '@/services/auth'
import { HttpClientLayer } from './httpClient'

const API_URL = process.env.NEXT_PUBLIC_API_URL || ''

const mainApi = new HttpClientLayer({
  baseURL: API_URL,
})

mainApi.addRequestInterceptor().use(async (config) => {
  const token = await getToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

export default mainApi
