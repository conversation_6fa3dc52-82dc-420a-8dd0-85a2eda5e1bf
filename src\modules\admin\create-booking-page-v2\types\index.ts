/* eslint-disable no-unused-vars */
import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'

// Utility type for deep partial objects
export type DeepPartial<T> = T extends object ? {
  [P in keyof T]?: DeepPartial<T[P]>
} : T

// Page Information Types
export interface PageInfo {
  name: string
  description: string
  slug: string
}

// Template Configuration Types
export interface TemplateConfig {
  id: string
  name: string
  category: string
  preview: string
  description: string
  features?: string[]
}

// Field Types
export type FieldType = 'football' | 'tennis' | 'badminton' | 'basketball'

export interface BookingField {
  id: string
  name: string
  type: FieldType
  capacity: number
}

// Contact Information Types
export interface ContactInfo {
  phone: string
  email: string
  socialLinks: {
    facebook?: string
    instagram?: string
    website?: string
  }
}

// Pricing Configuration Types
export interface PricingConfig {
  basePrice: number
  currency: string
  priceUnit: string // 'hour', 'session', 'day'
  showPricing: boolean
}

export interface BannerConfig {
  title: string
  subtitle: string
  image: string
  backgroundColor?: string
  imageFit?: 'cover' | 'contain'
}

// Booking Configuration Types
export interface BookingConfig {
  banner: BannerConfig

  // Operating hours
  businessHours: {
    start: string
    end: string
    daysOfWeek: number[]
  }

  // Slot duration in minutes
  slotDuration: number

  // Fields configuration
  fields: BookingField[]

  // Description settings
  description: {
    title?: string
    content?: string
    location?: string
  }

  // Contact information
  contactInfo: ContactInfo

  // Pricing configuration
  pricing: PricingConfig

  // Display settings
  showCapacity: boolean
  showFieldTypes: boolean
  showDirections: boolean
}

// Step Configuration
export interface StepConfig {
  id: number
  title: string
  icon: any // Lucide icon component
  isCompleted: boolean
  isActive: boolean
}

// Main Store State
export interface CreateBookingPageV2State {
  // Current step
  currentStep: number

  // Step data
  pageInfo: PageInfo
  selectedTemplateId: string
  bookingConfig: BookingConfig

  // UI state
  isLoading: boolean
  errors: Record<string, string>

  // Persist toggle
  shouldPersist: boolean
  setShouldPersist: (value: boolean) => void

  // Actions
  setCurrentStep: (step: number) => void
  nextStep: () => void
  prevStep: () => void

  // Page info actions
  updatePageInfo: (info: Partial<PageInfo>) => void
  // Template actions
  setSelectedTemplate: (templateId: string) => void
  loadTemplateConfig: (templateId: string) => void
  getTemplateConfig: (templateId: string) => DeepPartial<BookingConfig> | null

  // Booking config actions
  updateBookingConfig: (config: DeepPartial<BookingConfig>) => void
  addField: () => void
  removeField: (fieldId: string) => void
  updateField: (fieldId: string, updates: Partial<BookingField>) => void

  // Validation
  validateCurrentStep: () => boolean

  // Reset
  reset: () => void
  resetToDefaultConfig: () => void

  // Initialize from existing data (for edit mode)
  initializeFromBookingPage: (bookingPageData: BookingPageItem, currentStep?: number) => void
}

// Template Category
export enum TemplateCategory {
  SPORT = 'SPORT',
  EVENT = 'EVENT',
  RESTAURANT = 'RESTAURANT',
  BEAUTY = 'BEAUTY',
  EDUCATION = 'EDUCATION',
}

// Validation Rules
export interface ValidationRule {
  field: string
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}

export interface StepValidation {
  step: number
  rules: ValidationRule[]
}

// Form Data Types for react-hook-form
export interface PageInfoFormData {
  name: string
  description: string
  slug: string
}

export interface DomainConfigFormData {
  subdomain: string
  customDomain?: string
}

export interface BookingConfigFormData {
  bannerTitle: string
  bannerSubtitle: string
  bannerImage: string
  openTime: string
  closeTime: string
  fields: BookingField[]
}
