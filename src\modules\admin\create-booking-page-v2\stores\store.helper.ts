import type { BookingConfig, BookingField, PageInfo } from '../types'

// ===== TEMPLATE HELPERS =====
export const getTemplateConfigs = (): Record<string, Partial<BookingConfig>> => ({
  'sport-football': {
    banner: {
      title: 'Đặt sân bóng đá',
      subtitle: 'Sân bóng đá chất lượng cao',
      image: '',
    },
    description: 'Hệ thống đặt sân bóng đá chuyên nghiệp với các sân cỏ nhân tạo chất lượng cao.',
    fields: [
      { id: 'field-1', name: 'Sân bóng 1', type: 'football', capacity: 1 },
      { id: 'field-2', name: 'Sân bóng 2', type: 'football', capacity: 1 },
    ],
    pricing: {
      basePrice: 300000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'sport-tennis': {
    banner: {
      title: 'Đặt sân tennis',
      subtitle: 'Sân tennis đẳng cấp quốc tế',
      image: '',
    },
    description: '<PERSON><PERSON> thống đặt sân tennis với các sân đẳng cấp quốc tế, phù hợp cho mọi trình độ.',
    fields: [
      { id: 'field-1', name: 'Sân tennis 1', type: 'tennis', capacity: 1 },
      { id: 'field-2', name: 'Sân tennis 2', type: 'tennis', capacity: 1 },
    ],
    pricing: {
      basePrice: 500000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'sport-badminton': {
    banner: {
      title: 'Đặt sân cầu lông',
      subtitle: 'Sân cầu lông tiêu chuẩn',
      image: '',
    },
    description: 'Hệ thống đặt sân cầu lông với các sân tiêu chuẩn, ánh sáng tốt.',
    fields: [
      { id: 'field-1', name: 'Sân cầu lông 1', type: 'badminton', capacity: 1 },
      { id: 'field-2', name: 'Sân cầu lông 2', type: 'badminton', capacity: 1 },
      { id: 'field-3', name: 'Sân cầu lông 3', type: 'badminton', capacity: 1 },
    ],
    pricing: {
      basePrice: 200000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'sport-basketball': {
    banner: {
      title: 'Đặt sân bóng rổ',
      subtitle: 'Sân bóng rổ trong nhà',
      image: '',
    },
    description: 'Hệ thống đặt sân bóng rổ trong nhà với sàn gỗ chuyên nghiệp.',
    fields: [
      { id: 'field-1', name: 'Sân bóng rổ 1', type: 'basketball', capacity: 1 },
    ],
    pricing: {
      basePrice: 400000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
  },
  'event-conference': {
    banner: {
      title: 'Đặt phòng họp',
      subtitle: 'Phòng họp chuyên nghiệp',
      image: '',
    },
    description: 'Hệ thống đặt phòng họp với trang thiết bị hiện đại, phù hợp cho các cuộc họp quan trọng.',
    fields: [
      { id: 'field-1', name: 'Phòng họp A', type: 'football', capacity: 20 },
      { id: 'field-2', name: 'Phòng họp B', type: 'football', capacity: 10 },
    ],
    pricing: {
      basePrice: 1000000,
      currency: 'VNĐ',
      priceUnit: 'hour',
      showPricing: true,
    },
    showCapacity: true,
    showFieldTypes: false,
    showDirections: true,
  },
  'restaurant-booking': {
    banner: {
      title: 'Đặt bàn nhà hàng',
      subtitle: 'Nhà hàng sang trọng',
      image: '',
    },
    description: 'Hệ thống đặt bàn nhà hàng với không gian sang trọng, ẩm thực đa dạng.',
    fields: [
      { id: 'field-1', name: 'Bàn 2 người', type: 'football', capacity: 2 },
      { id: 'field-2', name: 'Bàn 4 người', type: 'football', capacity: 4 },
      { id: 'field-3', name: 'Bàn 6 người', type: 'football', capacity: 6 },
    ],
    pricing: {
      basePrice: 0,
      currency: 'VNĐ',
      priceUnit: 'session',
      showPricing: false,
    },
    showCapacity: true,
    showFieldTypes: false,
    showDirections: true,
  },
})

export const getTemplateConfig = (templateId: string): Partial<BookingConfig> | null => {
  const configs = getTemplateConfigs()
  return configs[templateId] || null
}

// ===== FIELD HELPERS =====
export const createNewField = (existingFields: BookingField[]): BookingField => ({
  id: `field-${Date.now()}`,
  name: `Sân ${existingFields.length + 1}`,
  type: 'football',
  capacity: 1,
})

export const updateFieldInArray = (
  fields: BookingField[],
  fieldId: string,
  updates: Partial<BookingField>,
): BookingField[] => {
  return fields.map(field =>
    field.id === fieldId ? { ...field, ...updates } : field,
  )
}

export const removeFieldFromArray = (fields: BookingField[], fieldId: string): BookingField[] => {
  return fields.filter(field => field.id !== fieldId)
}

// ===== VALIDATION HELPERS =====
export const getValidationDataForStep = (
  step: number,
  pageInfo: PageInfo,
  selectedTemplateId: string,
  bookingConfig: BookingConfig,
): any => {
  switch (step) {
    case 1:
      return pageInfo
    case 2:
      return { selectedTemplateId }
    case 3:
      return bookingConfig
    default:
      return null
  }
}

export const validateCurrentStepData = (
  step: number,
  pageInfo: PageInfo,
  selectedTemplateId: string,
  bookingConfig: BookingConfig,
): boolean => {
  const data = getValidationDataForStep(step, pageInfo, selectedTemplateId, bookingConfig)
  if (!data) {
    return false
  }

  return true
}

// ===== STATE HELPERS =====
export const clearErrorsForKeys = (errors: Record<string, string>, keys: string[]): Record<string, string> => {
  return keys.reduce((acc, key) => ({ ...acc, [key]: '' }), { ...errors })
}

export const hasConfigChanges = (currentConfig: BookingConfig, newConfig: Partial<BookingConfig>): boolean => {
  return Object.keys(newConfig).some(key =>
    currentConfig[key as keyof BookingConfig] !== newConfig[key as keyof BookingConfig],
  )
}

// ===== MIGRATION HELPERS =====
export const migrateBookingConfig = (persistedConfig: any, version: number): any => {
  if (version === 0 || version === 1) {
    return {
      ...persistedConfig,
      description: persistedConfig?.description || '',
      location: persistedConfig?.location || '',
      contactInfo: persistedConfig?.contactInfo || {
        phone: '',
        email: '',
        address: '',
        socialLinks: {
          facebook: '',
          instagram: '',
          website: '',
        },
      },
      pricing: persistedConfig?.pricing || {
        basePrice: 300000,
        currency: 'VNĐ',
        priceUnit: 'hour',
        showPricing: true,
      },
      showCapacity: persistedConfig?.showCapacity ?? true,
      showFieldTypes: persistedConfig?.showFieldTypes ?? true,
      showDirections: persistedConfig?.showDirections ?? true,
    }
  }
  return persistedConfig
}

export const migrateState = (persistedState: any, version: number): any => {
  if (version === 0 || version === 1) {
    return {
      ...persistedState,
      bookingConfig: migrateBookingConfig(persistedState.bookingConfig, version),
    }
  }
  return persistedState
}
