'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2/stores/create-booking-page-v2.store'
import { Clock } from 'lucide-react'
import React from 'react'

export const OperatingHoursConfig: React.FC = React.memo(() => {
  // Use direct store access
  const businessHours = useCreateBookingPageV2Store(state => state.bookingConfig?.businessHours)

  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)

  const handleOpenTimeChange = (value: string) => {
    updateBookingConfig({ businessHours: { ...businessHours, start: value } })
  }

  const handleCloseTimeChange = (value: string) => {
    updateBookingConfig({ businessHours: { ...businessHours, end: value } })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Giờ hoạt động
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="open-time">Giờ mở cửa</Label>
            <Input
              id="open-time"
              type="time"
              value={businessHours?.start}
              onChange={e => handleOpenTimeChange(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="close-time">Giờ đóng cửa</Label>
            <Input
              id="close-time"
              type="time"
              value={businessHours?.end}
              onChange={e => handleCloseTimeChange(e.target.value)}
            />
          </div>
        </div>
        <p className="text-sm text-gray-600">
          Lịch đặt sân sẽ hiển thị các khung giờ từ
          {' '}
          {businessHours?.start}
          {' '}
          đến
          {' '}
          {businessHours?.end}
        </p>
      </CardContent>
    </Card>
  )
})

OperatingHoursConfig.displayName = 'OperatingHoursConfig'
