'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2/stores/create-booking-page-v2.store'
import { Clock } from 'lucide-react'
import React from 'react'

// Slot duration options
const SLOT_DURATION_OPTIONS = Array.from({ length: 18 }, (_, i) => {
  const value = 15 + i * 5
  const hours = Math.floor(value / 60)
  const minutes = value % 60
  let label = ''
  if (hours > 0) {
    label = `${hours}h ${minutes > 0 ? minutes : '0'} phút`
  } else {
    label = `${minutes} phút`
  }
  return { value, label }
})

export const OperatingHoursConfig: React.FC = React.memo(() => {
  // Use direct store access
  const businessHours = useCreateBookingPageV2Store(state => state.bookingConfig?.businessHours)
  const slotDuration = useCreateBookingPageV2Store(state => state.bookingConfig?.slotDuration || 60)

  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)

  const handleOpenTimeChange = (value: string) => {
    updateBookingConfig({ businessHours: { ...businessHours, start: value } })
  }

  const handleCloseTimeChange = (value: string) => {
    updateBookingConfig({ businessHours: { ...businessHours, end: value } })
  }

  const handleSlotDurationChange = (value: string) => {
    updateBookingConfig({ slotDuration: Number(value) })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Clock className="w-4 h-4" />
          Giờ hoạt động & Thời gian slot
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="open-time">Giờ mở cửa</Label>
            <Input
              id="open-time"
              type="time"
              value={businessHours?.start}
              onChange={e => handleOpenTimeChange(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="close-time">Giờ đóng cửa</Label>
            <Input
              id="close-time"
              type="time"
              value={businessHours?.end}
              onChange={e => handleCloseTimeChange(e.target.value)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="slot-duration">Thời gian mỗi slot</Label>
          <Select value={slotDuration.toString()} onValueChange={handleSlotDurationChange}>
            <SelectTrigger id="slot-duration">
              <SelectValue placeholder="Chọn thời gian slot" />
            </SelectTrigger>
            <SelectContent>
              {SLOT_DURATION_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-gray-600 space-y-1">
          <p>
            Lịch đặt sân sẽ hiển thị các khung giờ từ
            {' '}
            <span className="font-medium">{businessHours?.start}</span>
            {' '}
            đến
            {' '}
            <span className="font-medium">{businessHours?.end}</span>
          </p>
          <p>
            Mỗi slot có thời gian:
            {' '}
            <span className="font-medium">
              {SLOT_DURATION_OPTIONS.find(opt => opt.value === slotDuration)?.label || '60 phút'}
            </span>
          </p>
        </div>
      </CardContent>
    </Card>
  )
})

OperatingHoursConfig.displayName = 'OperatingHoursConfig'
