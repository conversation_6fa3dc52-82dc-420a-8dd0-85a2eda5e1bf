import { motion } from 'framer-motion'
import React from 'react'

const LoadingFullscreen = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center"
    >
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full"
      />
    </motion.div>
  )
}

export default LoadingFullscreen
