'use client'

import type { LoadingModalData } from '../modal-store'
import { Button } from '@/components/ui/button'
import { Loader2, X } from 'lucide-react'

interface LoadingModalProps extends LoadingModalData {
  onClose: () => void
}

export function LoadingModal({
  title = 'Loading...',
  message = 'Please wait while we process your request.',
  showSpinner = true,
  allowClose = false,
  onClose,
}: LoadingModalProps) {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={allowClose ? onClose : undefined}
      />

      {/* Modal Content */}
      <div className="relative bg-white dark:bg-gray-900 rounded-lg shadow-xl p-6 mx-4 max-w-md w-full">
        {/* Close Button */}
        {allowClose && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        )}

        {/* Content */}
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Spinner */}
          {showSpinner && (
            <div className="flex items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          )}

          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>

          {/* Message */}
          {message && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {message}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
