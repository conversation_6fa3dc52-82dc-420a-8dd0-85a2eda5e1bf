'use client'

import LoadingFullscreen from '@/components/loading/loading-fullscreen'
import dynamic from 'next/dynamic'
import React from 'react'

// layzy load dashboard screen
const DashboardScreenDynamic = dynamic(() => import('@/modules/admin/dashboard/screens/DashboardScreen'), {
  ssr: false,
  loading: LoadingFullscreen,
})
export default function AgentDashboardPage() {
  return (<DashboardScreenDynamic />)
}
