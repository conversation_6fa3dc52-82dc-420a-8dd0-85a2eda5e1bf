'use client'

import { cn } from '@/lib/utils'
import React from 'react'
import {
  BannerSection,
  BookingFormSection,
  ContactSection,
  DescriptionSection,
  GridSlotSection,
  MapSection,
} from './layouts'

interface ModernSportTemplateProps {
  className?: string
}

/**
 * Modern Sport Template Component
 *
 * Layout structure:
 * -----------------
 * |Banner image   |
 * -----------------
 * |description|select date|
 * |show info booking|grid slot|
 * |contact|map|
 */
const ModernSportTemplate: React.FC<ModernSportTemplateProps> = React.memo(({
  className,
}) => {
  const isMobile = false
  const isLive = false

  return (
    <div className={cn(
      'w-full max-w-[100rem] mx-auto bg-white',
      isLive ? 'px-4 md:px-0' : isMobile ? 'p-4' : 'p-6',
      className,
    )}
    >
      {/* Banner Section - Full Width */}
      <BannerSection />

      {/* Description & Date Selection Row */}
      <div className={cn(
        'grid gap-6',
        'grid-cols-1',
      )}
      >
        <DescriptionSection />
      </div>

      {/* Booking Info & Grid Slot Row */}
      <div className={cn(
        'grid gap-6',
        'grid-cols-1',
      )}
      >
        {/* Grid Slot - 3 columns */}
        <div className={cn(
          isLive ? 'col-span-1 md:col-span-3' : isMobile ? 'col-span-1' : 'col-span-3',
        )}
        >
          <GridSlotSection />
        </div>
      </div>
      {/* Booking Form Section */}
      <BookingFormSection />
      {/* Contact & Map Row */}
      <div className={cn(
        'grid gap-6 py-6',
        'grid-cols-1 md:grid-cols-2',
      )}
      >
        {/* Contact Section */}
        <ContactSection />

        {/* Map Section */}
        <MapSection />
      </div>
    </div>
  )
})

export default ModernSportTemplate
