'use client'

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { MapPin, Settings } from 'lucide-react'
import React, { useState } from 'react'
import { useCreateBookingPageV2Store } from '../../../../stores/create-booking-page-v2.store'

// Helper function to extract src from Google Maps iframe
const extractMapSrc = (iframeCode: string): string => {
  const srcMatch = iframeCode.match(/src="([^"]*)"/)
  return srcMatch?.[1] || ''
}

export const DescriptionLocationConfig: React.FC = React.memo(() => {
  // Use direct store access
  const description = useCreateBookingPageV2Store(state => state.bookingConfig?.description)
  const updateBookingConfig = useCreateBookingPageV2Store(state => state.updateBookingConfig)

  // Local state for iframe input
  const [mapIframe, setMapIframe] = useState('')

  // Handler for map iframe input
  const handleMapIframeChange = (value: string) => {
    setMapIframe(value)
    const mapUrl = extractMapSrc(value)
    if (mapUrl) {
      updateBookingConfig({
        description: {
          ...description,
          mapUrl,
        },
      })
    }
  }
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Settings className="w-4 h-4" />
          Mô tả & Địa điểm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="description">Mô tả chi tiết</Label>
          <Textarea
            id="description"
            value={description?.content}
            onChange={e => updateBookingConfig({ description: { ...description, content: e.target.value } })}
            placeholder="VD: Sân thể thao hiện đại với đầy đủ tiện nghi..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="location">Địa điểm</Label>
          <Input
            id="location"
            value={description?.location}
            onChange={e => updateBookingConfig({ description: { ...description, location: e.target.value } })}
            placeholder="VD: Số 123, Đường ABC, Quận 1, TP.HCM"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="map-iframe" className="flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            Google Maps (Tùy chọn)
          </Label>
          <Textarea
            id="map-iframe"
            value={mapIframe}
            onChange={e => handleMapIframeChange(e.target.value)}
            placeholder="Dán iframe Google Maps vào đây..."
            rows={3}
            className="text-xs"
          />
          <p className="text-xs text-gray-500">
            Để lấy iframe: Vào Google Maps → Tìm địa điểm → Chia sẻ → Nhúng bản đồ → Sao chép HTML
          </p>
          {description?.mapUrl && (
            <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
              ✓ Đã lưu URL bản đồ thành công
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
})

DescriptionLocationConfig.displayName = 'DescriptionLocationConfig'
