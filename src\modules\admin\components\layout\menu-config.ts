import type { LucideIcon } from 'lucide-react'
import { appPaths } from '@/utils/app-routes'
import {
  BarChart3,
  Bell,
  Calendar,
  FileText,
  GitBranch,
  HelpCircle,
  Home,
  Settings,
  Shield,
  Users,
} from 'lucide-react'

export interface MenuItem {
  icon: LucideIcon
  label: string
  url: string
  isActive?: boolean
  badge?: string | number
  description?: string
}

export interface MenuSection {
  label: string
  items: MenuItem[]
}

// Main menu configuration - easy to maintain and extend
export const projectMenuConfig: MenuSection[] = [
  {
    label: 'Projects',
    items: [
      {
        icon: Home,
        label: 'Dashboard',
        url: appPaths.admin.dashboard(),
        isActive: true,
        description: 'Project overview and analytics',
      },
      {
        icon: GitBranch,
        label: 'Releases',
        url: '#',
        isActive: false,
        description: 'Manage project releases',
      },
      {
        icon: BarChart3,
        label: 'Analytics',
        url: '#',
        isActive: false,
        description: 'View project analytics',
      },
    ],
  },
  {
    label: 'Project Settings',
    items: [
      {
        icon: Users,
        label: 'Overview',
        url: '#',
        isActive: false,
        description: 'Project overview settings',
      },
      {
        icon: Shield,
        label: 'Access Tokens',
        url: '#',
        isActive: false,
        description: 'Manage API access tokens',
      },
      {
        icon: Users,
        label: 'Members',
        url: '#',
        isActive: false,
        description: 'Manage team members',
      },
      {
        icon: Settings,
        label: 'General',
        url: '#',
        isActive: false,
        description: 'General project settings',
      },
    ],
  },
  {
    label: 'Content',
    items: [
      {
        icon: FileText,
        label: 'Pages',
        url: '#',
        isActive: false,
        description: 'Manage booking pages',
      },
      {
        icon: Calendar,
        label: 'Bookings',
        url: '#',
        isActive: false,
        badge: '12',
        description: 'View and manage bookings',
      },
      {
        icon: Bell,
        label: 'Notifications',
        url: '#',
        isActive: false,
        badge: '3',
        description: 'Notification settings',
      },
    ],
  },
]

// Support menu items
export const supportMenuItems: MenuItem[] = [
  {
    icon: HelpCircle,
    label: 'Support',
    url: '#',
    description: 'Get help and support',
  },
]

// Utility function to add new menu item
export const addMenuItem = (sectionIndex: number, item: MenuItem) => {
  if (projectMenuConfig[sectionIndex]) {
    projectMenuConfig[sectionIndex].items.push(item)
  }
}

// Utility function to remove menu item
export const removeMenuItem = (sectionIndex: number, itemLabel: string) => {
  if (projectMenuConfig[sectionIndex]) {
    projectMenuConfig[sectionIndex].items = projectMenuConfig[sectionIndex].items.filter(
      item => item.label !== itemLabel,
    )
  }
}

// Utility function to update menu item active state
export const updateMenuItemActive = (itemLabel: string, isActive: boolean) => {
  projectMenuConfig.forEach((section) => {
    section.items.forEach((item) => {
      if (item.label === itemLabel) {
        item.isActive = isActive
      } else {
        item.isActive = false // Ensure only one item is active
      }
    })
  })
}

// Utility function to get current active item
export const getActiveMenuItem = (): MenuItem | null => {
  for (const section of projectMenuConfig) {
    for (const item of section.items) {
      if (item.isActive) {
        return item
      }
    }
  }
  return null
}
