'use client'

import type { UploadFilePayload } from '../../apis/booking-page.api'
import { useCallback, useState } from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'

interface UseImageUploadReturn {
  isUploading: boolean
  uploadProgress: number
  uploadImage: (file: File) => Promise<string | null>
  error: string | null
}

export const useImageUpload = (): UseImageUploadReturn => {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const uploadImage = useCallback(async (file: File): Promise<string | null> => {
    try {
      setIsUploading(true)
      setUploadProgress(0)
      setError(null)

      // Bước 1: Gọi API để lấy upload URL
      const uploadPayload: UploadFilePayload = {
        originalName: file.name,
        mimeType: file.type,
        category: 'banner', // hoặc có thể truyền từ bên ngoài
      }

      const response = await bookingPageAPIs.generateUploadUrl(uploadPayload)

      if (!response || !response.data || !response.data.uploadUrl || !response.data.publicUrl) {
        toast.error('Upload failed')
        return null
      }

      const { uploadUrl, publicUrl } = response.data

      // Bước 2: Upload file lên S3 hoặc storage service
      const formData = new FormData()
      formData.append('file', file)

      // Sử dụng XMLHttpRequest để theo dõi progress
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100)
            setUploadProgress(progress)
          }
        })

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            setUploadProgress(100)
            resolve(publicUrl)
          } else {
            reject(new Error(`Upload failed with status: ${xhr.status}`))
          }
        })

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'))
        })

        xhr.open('PUT', uploadUrl)
        xhr.setRequestHeader('Content-Type', file.type)
        xhr.send(file)
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed'
      setError(errorMessage)
      return null
    } finally {
      setIsUploading(false)
      // Reset progress sau một khoảng thời gian
      setTimeout(() => setUploadProgress(0), 2000)
    }
  }, [])

  return {
    isUploading,
    uploadProgress,
    uploadImage,
    error,
  }
}
