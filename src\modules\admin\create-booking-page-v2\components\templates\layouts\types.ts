import type { BookingConfig, PageInfo } from '../../../types'

// Base Layout Props
export interface BaseLayoutProps {
  config: BookingConfig
  pageInfo: PageInfo
  previewMode?: 'desktop' | 'mobile' | 'live'
  className?: string
}

// Banner Section Props
export interface BannerSectionProps {
  className?: string
}

// Description Section Props
export interface DescriptionSectionProps {
  className?: string
}

// Booking Info Section Props
export interface BookingInfoSectionProps extends BaseLayoutProps {
  showPricing?: boolean
  showCapacity?: boolean
  showFieldTypes?: boolean
}

// Grid Slot Section Props
export interface GridSlotSectionProps {
  className?: string
}

// Contact Section Props
export interface ContactSectionProps {
  className?: string
}

// Map Section Props
export interface MapSectionProps {
  className?: string
}

// Booking Form Section Props
export interface BookingFormSectionProps {
  className?: string
}

// Booking Form Data
export interface BookingFormData {
  customerName: string
  customerEmail: string
  customerPhone: string
  notes?: string
}
