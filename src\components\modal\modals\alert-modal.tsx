'use client'

import type { AlertModalData } from '../modal-store'
import { Button } from '@/components/ui/button'
import { AlertTriangle, CheckCircle, Info, X, XCircle } from 'lucide-react'

interface AlertModalProps extends AlertModalData {
  onClose: () => void
}

export function AlertModal({
  title = 'Alert',
  description = 'This is an alert message.',
  onOk,
  okText = 'OK',
  variant = 'default',
  onClose,
}: AlertModalProps) {
  const handleOk = () => {
    if (onOk) {
      onOk()
    }
    onClose()
  }

  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <XCircle className="h-6 w-6 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-600" />
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-600" />
      default:
        return <Info className="h-6 w-6 text-blue-600" />
    }
  }

  const getButtonVariant = () => {
    switch (variant) {
      case 'destructive':
        return 'destructive'
      case 'success':
        return 'default'
      default:
        return 'default'
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white dark:bg-gray-900 rounded-lg shadow-xl p-6 mx-4 max-w-md w-full">
        {/* Close Button */}
        <Button
          variant="ghost"
          size="icon"
          className="absolute top-2 right-2 h-8 w-8"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>

        {/* Content */}
        <div className="flex flex-col space-y-4">
          {/* Icon & Title */}
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {getIcon()}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h3>
          </div>

          {/* Description */}
          {description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 ml-9">
              {description}
            </p>
          )}

          {/* Actions */}
          <div className="flex justify-end pt-4">
            <Button
              variant={getButtonVariant()}
              onClick={handleOk}
            >
              {okText}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
