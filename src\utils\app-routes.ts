export const appPaths = {
  admin: {
    root: () => '/admin',
    dashboard: () => '/admin/pickslot',
    createBookingPage: () => '/admin/create-booking-page-v2',
    manageBookingPages: () => '/admin/manage-booking-pages',
    bookingPagePanel: (id: string) => `/admin/manage-booking-pages/${id}`,
    editBookingPage: (id: string) => `/admin/manage-booking-pages/${id}/edit`,
    // editBookingPageV2: (id: string) => `/admin/edit-booking-page-v2/${id}`,

    // page
    pagePanel: (pageId: string) => `/admin/pickslot/${pageId}/dashboard`,
    pageEdit: (pageId: string) => `/admin/pickslot/${pageId}/edit`,

    bookings: () => '/admin/bookings',
    bookingStats: () => '/admin/booking-stats',
    settings: () => '/admin/settings',
  },
  auth: {
    login: () => '/auth/signin',
    register: () => '/auth/signup',
  },
  public: {
    home: () => '/',
    bookingDetail: (id: string) => `/booking/${id}`,
  },
  marketing: {
    privacy: () => '/privacy',
    terms: () => '/terms',
  },
}
