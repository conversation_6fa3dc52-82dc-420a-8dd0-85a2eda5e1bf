'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useState } from 'react'
import { modalAPI } from '../modal-api'

export function ModalDemo() {
  const [loadingId, setLoadingId] = useState<string | null>(null)

  const handleShowLoading = () => {
    const id = modalAPI.showLoading({
      title: 'Processing...',
      message: 'Please wait while we save your data.',
      allowClose: true,
    })
    setLoadingId(id)

    // Auto close after 3 seconds
    setTimeout(() => {
      modalAPI.closeModal(id)
      setLoadingId(null)
    }, 3000)
  }

  const handleShowLoadingWithoutClose = () => {
    const id = modalAPI.showLoading({
      title: 'Uploading File...',
      message: 'This may take a few moments.',
      allowClose: false,
    })

    // Auto close after 5 seconds
    setTimeout(() => {
      modalAPI.closeModal(id)
    }, 5000)
  }

  const handleShowConfirm = () => {
    modalAPI.showConfirm({
      title: 'Save Changes',
      description: 'Do you want to save your changes before leaving?',
      confirmText: 'Save',
      cancelText: 'Discard',
      onConfirm: async () => {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        modalAPI.showSuccess('Changes saved successfully!')
      },
      onCancel: () => {
        modalAPI.showWarning('Changes were discarded.')
      },
    })
  }

  const handleShowDeleteConfirm = () => {
    modalAPI.showDeleteConfirm(
      async () => {
        // Simulate delete API call
        await new Promise(resolve => setTimeout(resolve, 1500))
        modalAPI.showSuccess('Item deleted successfully!')
      },
      'this important document',
    )
  }

  const handleShowAlert = () => {
    modalAPI.showAlert({
      title: 'Information',
      description: 'This is a general information alert.',
      variant: 'default',
    })
  }

  const handleShowSuccess = () => {
    modalAPI.showSuccess('Operation completed successfully!')
  }

  const handleShowError = () => {
    modalAPI.showError('Something went wrong. Please try again.')
  }

  const handleShowWarning = () => {
    modalAPI.showWarning('Please review your input before proceeding.')
  }

  const handleUpdateLoading = () => {
    if (loadingId) {
      modalAPI.updateModal(loadingId, {
        title: 'Almost Done...',
        message: 'Just a few more seconds.',
      })
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Modal Manager Demo</CardTitle>
          <CardDescription>
            Test different types of modals using the modal manager system.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Loading Modals */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Loading Modals</h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleShowLoading}>
                Show Loading (Closable)
              </Button>
              <Button onClick={handleShowLoadingWithoutClose}>
                Show Loading (Non-closable)
              </Button>
              {loadingId && (
                <Button variant="outline" onClick={handleUpdateLoading}>
                  Update Loading Message
                </Button>
              )}
            </div>
          </div>

          {/* Confirm Modals */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Confirm Modals</h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleShowConfirm}>
                Show Confirm
              </Button>
              <Button variant="destructive" onClick={handleShowDeleteConfirm}>
                Show Delete Confirm
              </Button>
            </div>
          </div>

          {/* Alert Modals */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Alert Modals</h3>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" onClick={handleShowAlert}>
                Show Alert
              </Button>
              <Button variant="default" onClick={handleShowSuccess}>
                Show Success
              </Button>
              <Button variant="destructive" onClick={handleShowError}>
                Show Error
              </Button>
              <Button variant="secondary" onClick={handleShowWarning}>
                Show Warning
              </Button>
            </div>
          </div>

          {/* Utility Actions */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Utility Actions</h3>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" onClick={modalAPI.closeAllModals}>
                Close All Modals
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
