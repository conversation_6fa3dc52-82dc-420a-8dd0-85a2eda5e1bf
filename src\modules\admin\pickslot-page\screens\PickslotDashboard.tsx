import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { useNavigation } from '@/hooks/useNavigation'
import { Header } from '@/modules/admin/components/layout-auth-v2/header'
import { TopNav } from '@/modules/admin/components/layout-auth-v2/top-nav'
import ProfileDropdown from '@/modules/admin/components/ProfileDropdown'
import { appPaths } from '@/utils/app-routes'
import { format } from 'date-fns'
import { BarChart3, Calendar, CalendarDays, Clock, Eye, Globe, Link as LinkIcon, Settings, Users } from 'lucide-react'
import { useCallback, useState } from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'
import { Main } from '../../components/layout-auth-v2/main'
import { usePickslotPage, usePickslotPageError, usePickslotPageLoading, useSetPickslotPage } from '../stores/booking-page.store'

function PickslotDashboard() {
  const bookingPage = usePickslotPage()
  const setBookingPage = useSetPickslotPage()
  const isLoading = usePickslotPageLoading()
  const error = usePickslotPageError()
  const { navigate } = useNavigation()
  const [isPublishing, setIsPublishing] = useState(false)

  const handleCopyLink = () => {
    if (bookingPage?.slug) {
      const bookingUrl = `${window.location.origin}/${bookingPage.slug}`
      navigator.clipboard.writeText(bookingUrl)
      toast.success('Link đã được copy vào clipboard!')
    }
  }

  const handleViewBookingPage = () => {
    if (bookingPage?.slug) {
      const bookingUrl = `${window.location.origin}/${bookingPage.slug}?isPreview=true`
      window.open(bookingUrl, '_blank')
    }
  }

  const handleTogglePublish = useCallback(async () => {
    if (!bookingPage?._id) {
      return
    }

    setIsPublishing(true)
    try {
      const isCurrentlyPublic = bookingPage.isPublic
      const res = await bookingPageAPIs.toggleBookingPagePublic(bookingPage._id, !isCurrentlyPublic)

      if (res.status?.success) {
        toast.success(isCurrentlyPublic ? 'Đã ẩn trang booking' : 'Đã publish trang booking')
        setBookingPage(res.data)
      }
    } catch (error) {
      console.error('Error toggling publish status:', error)
      toast.error('Có lỗi xảy ra khi thay đổi trạng thái publish')
    } finally {
      setIsPublishing(false)
    }
  }, [bookingPage?._id, bookingPage?.isPublic])

  if (error) {
    return (
      <Alert variant="destructive" className="m-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50/95">
      <Header className="sticky top-0 z-10 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <TopNav links={topNav} />
        <div className="ml-auto flex items-center space-x-4">
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="grid gap-6 sm:gap-8">
          {/* Overview Section */}
          <Card className="border-none bg-white/50 backdrop-blur-sm supports-[backdrop-filter]:bg-white/80">
            <CardHeader className="pb-2">
              <div className="flex flex-col gap-6 sm:flex-row sm:items-start sm:justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-xl font-bold tracking-tight sm:text-2xl">
                    {isLoading ? <Skeleton className="h-7 w-48 sm:h-8" /> : bookingPage?.name || 'My Booking Page'}
                  </CardTitle>
                  <CardDescription className="text-sm sm:text-base">
                    {isLoading ? <Skeleton className="h-4 w-full max-w-[24rem]" /> : bookingPage?.description || 'No description available'}
                  </CardDescription>
                  {!isLoading && bookingPage && (
                    <div className="flex flex-wrap items-center gap-2 pt-2">
                      <Badge variant={bookingPage.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                        {bookingPage.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        Tạo ngày:
                        {' '}
                        {format(new Date(bookingPage.createdAt), 'dd/MM/yyyy')}
                      </Badge>
                    </div>
                  )}
                </div>
                <div className="flex flex-col-reverse gap-3 sm:flex-row">
                  <Button
                    variant="outline"
                    className="flex w-full items-center justify-center gap-2 shadow-sm transition-all hover:bg-gray-50 sm:w-auto"
                    onClick={handleCopyLink}
                    disabled={isLoading || !bookingPage?.slug}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Copy Link
                  </Button>
                  <Button
                    variant="outline"
                    className="flex w-full items-center justify-center gap-2 shadow-sm transition-all hover:bg-gray-50 sm:w-auto"
                    onClick={handleViewBookingPage}
                    disabled={isLoading || !bookingPage?.slug}
                  >
                    <Eye className="h-4 w-4" />
                    Xem Preview
                  </Button>
                  <Button
                    variant={bookingPage?.isPublic ? 'destructive' : 'default'}
                    className={`flex w-full items-center justify-center gap-2 shadow-lg transition-all hover:scale-105 sm:w-auto ${
                      bookingPage?.isPublic
                        ? 'bg-red-600 hover:bg-red-700 text-white shadow-red-200 hover:shadow-red-300'
                        : 'bg-green-600 hover:bg-green-700 text-white shadow-green-200 hover:shadow-green-300'
                    }`}
                    onClick={handleTogglePublish}
                    disabled={isLoading || isPublishing || !bookingPage?._id}
                  >
                    {isPublishing
                      ? (
                          <Skeleton className="h-4 w-4" />
                        )
                      : (
                          <Globe className="h-4 w-4" />
                        )}
                    {isPublishing
                      ? 'Đang xử lý...'
                      : (
                          bookingPage?.isPublic ? 'Ẩn trang' : 'Publish trang'
                        )}
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-4 sm:pt-6">
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                {/* Stats Cards */}
                <Card className="overflow-hidden bg-white">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-blue-100 p-2">
                        <CalendarDays className="h-4 w-4 text-blue-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Template</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-base font-semibold tracking-tight sm:text-lg">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : <span className="capitalize">{bookingPage?.templateCode || 'Default'}</span>}
                    </div>
                  </CardContent>
                </Card>

                <Card className="overflow-hidden bg-white">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-green-100 p-2">
                        <Clock className="h-4 w-4 text-green-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Status</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-base font-semibold tracking-tight sm:text-lg">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : (
                            <div className="flex items-center gap-2">
                              <div className={`h-2 w-2 rounded-full ${bookingPage?.status === 'active' ? 'bg-green-500' : 'bg-red-500'}`} />
                              <span className={`capitalize ${bookingPage?.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                                {bookingPage?.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                              </span>
                            </div>
                          )}
                    </div>
                  </CardContent>
                </Card>

                <Card className="overflow-hidden bg-white">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-purple-100 p-2">
                        <Users className="h-4 w-4 text-purple-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Plan</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-base font-semibold tracking-tight sm:text-lg">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : <span className="capitalize">{bookingPage?.plan || 'Free'}</span>}
                    </div>
                  </CardContent>
                </Card>

                <Card className="overflow-hidden bg-white">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-orange-100 p-2">
                        <Calendar className="h-4 w-4 text-orange-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Ngày tạo</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-sm font-semibold tracking-tight">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : bookingPage?.createdAt ? format(new Date(bookingPage.createdAt), 'dd/MM/yyyy') : 'N/A'}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Additional Information Section */}
              {!isLoading && bookingPage && (
                <div className="mt-8 grid gap-6 sm:grid-cols-2">
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Settings className="h-5 w-5" />
                        Thông tin chi tiết
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Slug:</span>
                          <span className="text-sm font-medium">{bookingPage.slug}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Template:</span>
                          <Badge variant="outline" className="text-xs">
                            {bookingPage.templateCode}
                          </Badge>
                        </div>

                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">URL đầy đủ:</span>
                          <span className="text-sm font-medium text-blue-600">
                            {`${window.location.origin}/${bookingPage.slug}`}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <BarChart3 className="h-5 w-5" />
                        Thống kê nhanh
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Trạng thái:</span>
                          <Badge variant={bookingPage.status === 'active' ? 'default' : 'secondary'} className="text-xs">
                            {bookingPage.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Gói dịch vụ:</span>
                          <Badge variant="outline" className="text-xs">
                            {bookingPage.plan}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Ngày tạo:</span>
                          <span className="text-sm font-medium">
                            {format(new Date(bookingPage.createdAt), 'dd/MM/yyyy HH:mm')}
                          </span>
                        </div>

                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Quick Actions */}
              {!isLoading && bookingPage && (
                <div className="mt-8">
                  <Card className="bg-white">
                    <CardHeader>
                      <CardTitle className="text-lg">Hành động nhanh</CardTitle>
                      <CardDescription>
                        Quản lý và xem trang booking của bạn
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-3 mt-3">
                        <Button
                          onClick={handleViewBookingPage}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          Xem Preview
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleCopyLink}
                          className="flex items-center gap-2"
                        >
                          <LinkIcon className="h-4 w-4" />
                          Copy link
                        </Button>
                        <Button
                          variant="outline"
                          className="flex items-center gap-2"
                          onClick={() => {
                            navigate(appPaths.admin.pageEdit(bookingPage._id))
                          }}
                        >
                          <Settings className="h-4 w-4" />
                          Chỉnh sửa
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </Main>
    </div>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: 'dashboard',
    isActive: true,
    disabled: true,
  },
]

export default PickslotDashboard
