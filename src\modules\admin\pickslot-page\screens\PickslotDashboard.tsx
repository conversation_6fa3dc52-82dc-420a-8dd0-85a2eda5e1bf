import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Header } from '@/modules/admin/components/layout-auth-v2/header'
import { TopNav } from '@/modules/admin/components/layout-auth-v2/top-nav'
import ProfileDropdown from '@/modules/admin/components/ProfileDropdown'
import { CalendarDays, Clock, Link as LinkIcon, Users } from 'lucide-react'
import Link from 'next/link'
import { Main } from '../../components/layout-auth-v2/main'
import { usePickslotPage, usePickslotPageError, usePickslotPageLoading } from '../stores/booking-page.store'

function PickslotDashboard() {
  const bookingPage = usePickslotPage()
  const isLoading = usePickslotPageLoading()
  const error = usePickslotPageError()

  if (error) {
    return (
      <Alert variant="destructive" className="m-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50/95">
      <Header className="sticky top-0 z-10 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
        <TopNav links={topNav} />
        <div className="ml-auto flex items-center space-x-4">
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="grid gap-6 sm:gap-8">
          {/* Overview Section */}
          <Card className="border-none bg-white/50 backdrop-blur-sm supports-[backdrop-filter]:bg-white/80">
            <CardHeader className="pb-2">
              <div className="flex flex-col gap-6 sm:flex-row sm:items-start sm:justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-xl font-bold tracking-tight sm:text-2xl">
                    {isLoading ? <Skeleton className="h-7 w-48 sm:h-8" /> : bookingPage?.name || 'My Booking Page'}
                  </CardTitle>
                  <CardDescription className="text-sm sm:text-base">
                    {isLoading ? <Skeleton className="h-4 w-full max-w-[24rem]" /> : bookingPage?.description || 'No description available'}
                  </CardDescription>
                </div>
                <div className="flex flex-col-reverse gap-3 sm:flex-row">
                  <Button
                    variant="outline"
                    className="flex w-full items-center justify-center gap-2 shadow-sm transition-all hover:bg-gray-50 sm:w-auto"
                    onClick={() => {
                      if (bookingPage?.slug) {
                        navigator.clipboard.writeText(`${window.location.origin}/book/${bookingPage.slug}`)
                      }
                    }}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Copy Link
                  </Button>
                  <Button variant="default" className="w-full shadow-sm sm:w-auto" asChild>
                    <Link href={`/booking/${bookingPage?._id}/edit`}>
                      Edit Page
                    </Link>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-4 sm:pt-6">
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {/* Stats Cards */}
                <Card className="overflow-hidden bg-white">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-blue-100 p-2">
                        <CalendarDays className="h-4 w-4 text-blue-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Template</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-base font-semibold tracking-tight sm:text-lg">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : <span className="capitalize">{bookingPage?.templateCode || 'Default'}</span>}
                    </div>
                  </CardContent>
                </Card>

                <Card className="overflow-hidden bg-white">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-green-100 p-2">
                        <Clock className="h-4 w-4 text-green-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Status</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-base font-semibold tracking-tight sm:text-lg">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : (
                            <div className="flex items-center gap-2">
                              <div className={`h-2 w-2 rounded-full ${bookingPage?.status === 'active' ? 'bg-green-500' : 'bg-red-500'}`} />
                              <span className={`capitalize ${bookingPage?.status === 'active' ? 'text-green-600' : 'text-red-600'}`}>
                                {bookingPage?.status || 'N/A'}
                              </span>
                            </div>
                          )}
                    </div>
                  </CardContent>
                </Card>

                <Card className="overflow-hidden bg-white sm:col-span-2 lg:col-span-1">
                  <CardHeader className="flex flex-row items-center justify-between space-x-4 border-b bg-gray-50/50 p-4 pb-3 sm:pb-2">
                    <div className="flex items-center gap-3">
                      <div className="rounded-lg bg-purple-100 p-2">
                        <Users className="h-4 w-4 text-purple-600" />
                      </div>
                      <CardTitle className="text-sm font-medium">Plan</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    <div className="text-base font-semibold tracking-tight sm:text-lg">
                      {isLoading
                        ? <Skeleton className="h-6 w-24" />
                        : <span className="capitalize">{bookingPage?.plan || 'Free'}</span>}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions - đã được chuyển lên phần header */}

            </CardContent>
          </Card>
        </div>
      </Main>
    </div>
  )
}

const topNav = [
  {
    title: 'Overview',
    href: 'dashboard',
    isActive: true,
    disabled: true,
  },
]

export default PickslotDashboard
