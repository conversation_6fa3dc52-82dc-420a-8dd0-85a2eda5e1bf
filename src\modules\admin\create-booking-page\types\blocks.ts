/**
 * All available block types
 */
import type { LayoutType } from './theme'

export type BlockType =
  // Basic blocks
  | 'banner'
  | 'info'
  | 'description'
  // | 'booking_form'
  | 'map'
  // Advanced blocks
  | 'gallery'
  | 'testimonials'
  | 'faq'
  | 'features'
  | 'pricing'
  | 'countdown'
  | 'social_proof'
  | 'contact_form'
  | 'schedule'
  | 'speakers'
  | 'sponsors'
  | 'custom'
  // Sport field specific blocks
  | 'location_picker'
  | 'time_slot_picker'
  | 'field_selector'
  | 'availability_calendar'
  | 'booking_ticket'
  // Service specific blocks
  | 'service_selector'
  | 'staff_selector'
  // Transportation specific blocks
  | 'route_picker'
  | 'vehicle_selector'
  | 'config'

/**
 * Base block interface
 */
export interface Block<T = Record<string, any>> {
  id?: string
  type: BlockType
  data: T
  isVisible?: boolean
  settings?: {
    padding?: string
    margin?: string
    backgroundColor?: string
    textColor?: string
    borderRadius?: string
    animation?: string
  }
}

/**
 * Banner block for displaying a hero image with title and subtitle
 */
export interface BannerBlock extends Block {
  type: 'banner'
  data: {
    image: string
    title: string
    subtitle?: string
    overlayColor?: string
    overlayOpacity?: number
    buttonText?: string
    buttonUrl?: string
    alignment?: 'left' | 'center' | 'right'
    height?: 'small' | 'medium' | 'large' | 'full'
  }
}

/**
 * Info block for displaying event details
 */
export interface InfoBlock extends Block {
  type: 'info'
  data: {
    time: string
    location: string
    slots: number
    price: number
    currency?: string
    showRemainingSlots?: boolean
    organizer?: string
    contactEmail?: string
    contactPhone?: string
  }
}

/**
 * Description block for displaying rich text content
 */
export interface DescriptionBlock extends Block {
  type: 'description'
  data: {
    content: string
    enableRichText?: boolean
    showReadMore?: boolean
    maxLength?: number
  }
}

/**
 * Map block for displaying location
 */
export interface MapBlock extends Block {
  type: 'map'
  data: {
    iframe: string
    address?: string
    showDirectionsButton?: boolean
    mapHeight?: number
    zoom?: number
  }
}

/**
 * Gallery block for displaying multiple images
 */
export interface GalleryBlock extends Block {
  type: 'gallery'
  data: {
    images: Array<{
      url: string
      caption?: string
      alt?: string
    }>
    layout?: 'grid' | 'carousel' | 'masonry'
    columns?: 2 | 3 | 4
    enableLightbox?: boolean
  }
}

/**
 * Testimonials block for displaying customer reviews
 */
export interface TestimonialsBlock extends Block {
  type: 'testimonials'
  data: {
    testimonials: Array<{
      quote: string
      author: string
      role?: string
      avatar?: string
      rating?: number
    }>
    layout?: 'carousel' | 'grid' | 'list'
    autoplay?: boolean
  }
}

/**
 * FAQ block for displaying frequently asked questions
 */
export interface FaqBlock extends Block {
  type: 'faq'
  data: {
    title?: string
    faqs: Array<{
      question: string
      answer: string
    }>
    layout?: 'accordion' | 'list'
    expandAll?: boolean
  }
}

/**
 * Features block for highlighting key features
 */
export interface FeaturesBlock extends Block {
  type: 'features'
  data: {
    title?: string
    subtitle?: string
    features: Array<{
      title: string
      description: string
      icon?: string
      image?: string
    }>
    columns?: 2 | 3 | 4
    layout?: 'grid' | 'list' | 'alternating'
  }
}

/**
 * Pricing block for displaying pricing options
 */
export interface PricingBlock extends Block {
  type: 'pricing'
  data: {
    title?: string
    subtitle?: string
    plans: Array<{
      name: string
      price: number
      currency?: string
      period?: string
      features: string[]
      isPopular?: boolean
      buttonText?: string
      buttonUrl?: string
    }>
    columns?: 2 | 3 | 4
  }
}

/**
 * Countdown block for displaying time until event
 */
export interface CountdownBlock extends Block {
  type: 'countdown'
  data: {
    targetDate: string
    title?: string
    subtitle?: string
    showDays?: boolean
    showHours?: boolean
    showMinutes?: boolean
    showSeconds?: boolean
    expiredMessage?: string
  }
}

/**
 * Social proof block for displaying statistics and achievements
 */
export interface SocialProofBlock extends Block {
  type: 'social_proof'
  data: {
    title?: string
    subtitle?: string
    stats: Array<{
      value: string | number
      label: string
      icon?: string
    }>
    layout?: 'horizontal' | 'grid'
  }
}

/**
 * Contact form block for general inquiries
 */
export interface ContactFormBlock extends Block {
  type: 'contact_form'
  data: {
    title?: string
    subtitle?: string
    fields: Array<{
      name: string
      label: string
      type: 'text' | 'email' | 'phone' | 'textarea' | 'select' | 'checkbox'
      required?: boolean
      options?: string[]
    }>
    submitButtonText?: string
    successMessage?: string
  }
}

/**
 * Schedule block for displaying event agenda
 */
export interface ScheduleBlock extends Block {
  type: 'schedule'
  data: {
    title?: string
    subtitle?: string
    days: Array<{
      date: string
      sessions: Array<{
        time: string
        title: string
        description?: string
        speaker?: string
        location?: string
      }>
    }>
    layout?: 'tabs' | 'list' | 'timeline'
  }
}

/**
 * Speakers block for displaying event speakers
 */
export interface SpeakersBlock extends Block {
  type: 'speakers'
  data: {
    title?: string
    subtitle?: string
    speakers: Array<{
      name: string
      role?: string
      bio?: string
      image?: string
      social?: {
        twitter?: string
        linkedin?: string
        facebook?: string
        instagram?: string
      }
    }>
    columns?: 2 | 3 | 4
    layout?: 'grid' | 'list'
  }
}

/**
 * Sponsors block for displaying event sponsors
 */
export interface SponsorsBlock extends Block {
  type: 'sponsors'
  data: {
    title?: string
    subtitle?: string
    sponsors: Array<{
      name: string
      logo: string
      url?: string
      level?: 'platinum' | 'gold' | 'silver' | 'bronze' | string
    }>
    layout?: 'grid' | 'list'
    groupByLevel?: boolean
  }
}

/**
 * Custom block for any custom content
 */
export interface CustomBlock extends Block {
  type: 'custom'
  data: {
    content: string
    html?: string
    css?: string
    js?: string
  }
}

/**
 * Template interface
 */
export interface Template {
  id: string
  name: string
  templateCode: string
  description: string
  image: string
  blocks: Block[]
  theme?: {
    primaryColor: string
    fontFamily: string
    layout: LayoutType
    secondaryColor?: string
    textColor?: string
    backgroundColor?: string
    headerStyle?: 'default' | 'transparent' | 'sticky'
    footerStyle?: 'default' | 'minimal' | 'none'
    borderRadius?: string
    spacing?: 'compact' | 'normal' | 'spacious'
    animation?: string
    customCSS?: string
  }
  settings?: {
    seo?: {
      title?: string
      description?: string
      keywords?: string[]
      ogImage?: string
    }
    analytics?: {
      googleAnalyticsId?: string
      facebookPixelId?: string
    }
    integrations?: {
      zapier?: string
      slack?: string
      email?: string
    }
    advanced?: {
      customHead?: string
      customScripts?: string
    }
  }
}

/**
 * Location Picker block for selecting a location on a map
 */
export interface LocationPickerBlock extends Block {
  type: 'location_picker'
  data: {
    title?: string
    subtitle?: string
    locations: Array<{
      id: string
      name: string
      address: string
      coordinates: {
        lat: number
        lng: number
      }
      description?: string
      image?: string
      amenities?: string[]
    }>
    defaultLocation?: string
    mapZoom?: number
    mapHeight?: number
    showFilters?: boolean
    filterOptions?: Array<{
      name: string
      label: string
      options: string[]
    }>
    layout?: 'map_with_list' | 'list_with_map' | 'map_only' | 'list_only'
  }
}

/**
 * Time Slot Picker block for selecting time slots
 */
export interface TimeSlotPickerBlock extends Block {
  type: 'time_slot_picker'
  data: {
    title?: string
    subtitle?: string
    slotDuration: number // in minutes
    startTime: string // HH:MM format
    endTime: string // HH:MM format
    daysInAdvance: number
    maxDaysToShow: number
    disabledDays?: number[] // 0 = Sunday, 1 = Monday, etc.
    specialDays?: Array<{
      date: string // YYYY-MM-DD format
      startTime?: string
      endTime?: string
      isDisabled?: boolean
      note?: string
    }>
    showAvailability?: boolean
    layout?: 'calendar' | 'list' | 'timeline'
    allowMultipleSelection?: boolean
    maxSelections?: number
  }
}

/**
 * Field Selector block for selecting a specific field or court
 */
export interface FieldSelectorBlock extends Block {
  type: 'field_selector'
  data: {
    title?: string
    subtitle?: string
    fields: Array<{
      id: string
      name: string
      type: string // e.g., 'football', 'tennis', 'basketball'
      image?: string
      capacity?: number
      pricePerHour?: number
      amenities?: string[]
      isIndoor?: boolean
    }>
    defaultField?: string
    showFilters?: boolean
    filterOptions?: Array<{
      name: string
      label: string
      options: string[]
    }>
    layout?: 'grid' | 'list' | 'tabs'
    columns?: 2 | 3 | 4
  }
}

/**
 * Availability Calendar block for displaying and selecting available dates and times
 */
export interface AvailabilityCalendarBlock extends Block {
  type: 'availability_calendar'
  data: {
    title?: string
    subtitle?: string
    view?: 'month' | 'week' | 'day'
    defaultView?: 'month' | 'week' | 'day'
    minDate?: string // YYYY-MM-DD format
    maxDate?: string // YYYY-MM-DD format
    disabledDates?: string[] // Array of YYYY-MM-DD strings
    highlightedDates?: Array<{
      date: string
      color?: string
      tooltip?: string
    }>
    businessHours?: {
      start: string // HH:MM format
      end: string // HH:MM format
      daysOfWeek: number[] // 0 = Sunday, 1 = Monday, etc.
    }
    showWeekends?: boolean
    firstDayOfWeek?: 0 | 1 // 0 = Sunday, 1 = Monday
    timeSlotInterval?: number // in minutes
    showAvailabilityLegend?: boolean
    fields?: Array<{
      id: string
      name: string
      type: string
      capacity: number
      pricePerHour: number
      isIndoor?: boolean
    }>
    configMode?: 'common' | 'individual'
    commonConfig?: {
      pricePerHour?: number
      dynamicPricing?: {
        enabled: boolean
        timeBasedPrices?: Array<{
          startTime: string
          endTime: string
          price: number
        }>
        dayBasedPrices?: Array<{
          day: number
          price: number
        }>
        combinedRules?: Array<{
          id: string
          name: string
          days: number[]
          startTime: string
          endTime: string
          price: number
        }>
      }
    }
  }
}

/**
 * Booking Ticket block for displaying booking summary and checkout options
 */
export interface BookingTicketBlock extends Block {
  type: 'booking_ticket'
  data: {
    title?: string
    subtitle?: string
    showPriceBreakdown?: boolean
    showBookingSummary?: boolean
    ctaButtonText?: string
    fields?: string[] // Fields to display in the form
    paymentMethods?: string[] // Payment methods to offer
  }
}

/**
 * Service Selector block for selecting services
 */
export interface ServiceSelectorBlock extends Block {
  type: 'service_selector'
  data: {
    title?: string
    subtitle?: string
    services: Array<{
      id: string
      name: string
      description?: string
      duration: number // in minutes
      price: number
      image?: string
      category?: string
      popular?: boolean
    }>
    categories?: Array<{
      id: string
      name: string
    }>
    showCategories?: boolean
    layout?: 'grid' | 'list' | 'cards'
    columns?: 2 | 3 | 4
    showPrices?: boolean
    showDuration?: boolean
    allowMultipleSelection?: boolean
  }
}

/**
 * Staff Selector block for selecting staff members
 */
export interface StaffSelectorBlock extends Block {
  type: 'staff_selector'
  data: {
    title?: string
    subtitle?: string
    staff: Array<{
      id: string
      name: string
      role?: string
      bio?: string
      image?: string
      specialties?: string[]
      availability?: Array<{
        day: number // 0 = Sunday, 1 = Monday, etc.
        startTime: string // HH:MM format
        endTime: string // HH:MM format
      }>
    }>
    showAvailability?: boolean
    layout?: 'grid' | 'list' | 'cards'
    columns?: 2 | 3 | 4
    allowAnyStaff?: boolean
  }
}

/**
 * Route Picker block for selecting routes
 */
export interface RoutePickerBlock extends Block {
  type: 'route_picker'
  data: {
    title?: string
    subtitle?: string
    showMap?: boolean
    mapHeight?: number
    pickupLabel?: string
    dropoffLabel?: string
    useCurrentLocation?: boolean
    savedLocations?: Array<{
      id: string
      name: string
      address: string
      coordinates: {
        lat: number
        lng: number
      }
      type: 'home' | 'work' | 'favorite' | 'other'
    }>
    popularDestinations?: Array<{
      id: string
      name: string
      address: string
      coordinates: {
        lat: number
        lng: number
      }
      image?: string
    }>
    showEstimatedDistance?: boolean
    showEstimatedTime?: boolean
    showEstimatedPrice?: boolean
  }
}

/**
 * Vehicle Selector block for selecting vehicles
 */
export interface VehicleSelectorBlock extends Block {
  type: 'vehicle_selector'
  data: {
    title?: string
    subtitle?: string
    vehicles: Array<{
      id: string
      name: string
      type: string // e.g., 'sedan', 'suv', 'van'
      image?: string
      capacity: number
      price: number
      priceType: 'fixed' | 'per_km' | 'per_minute'
      description?: string
      features?: string[]
    }>
    layout?: 'grid' | 'list' | 'tabs'
    showPrices?: boolean
    showCapacity?: boolean
    showFeatures?: boolean
  }
}

/**
 * Union type for all block types
 */
export type AnyBlock =
  | BannerBlock
  | InfoBlock
  | DescriptionBlock
  | MapBlock
  | GalleryBlock
  | TestimonialsBlock
  | FaqBlock
  | FeaturesBlock
  | PricingBlock
  | CountdownBlock
  | SocialProofBlock
  | ContactFormBlock
  | ScheduleBlock
  | SpeakersBlock
  | SponsorsBlock
  | CustomBlock
  // Sport field specific blocks
  | LocationPickerBlock
  | TimeSlotPickerBlock
  | FieldSelectorBlock
  | AvailabilityCalendarBlock
  | BookingTicketBlock
  // Service specific blocks
  | ServiceSelectorBlock
  | StaffSelectorBlock
  // Transportation specific blocks
  | RoutePickerBlock
  | VehicleSelectorBlock
