'use client'

import type { BaseLayoutProps } from './layouts/types'
import { Skeleton } from '@/components/ui/skeleton'
import React, { lazy, Suspense } from 'react'
import { useCreateBookingPageV2Store } from '../../stores/create-booking-page-v2.store'

// Lazy load template components
const ModernSportTemplate = lazy(() => import('./ModernSportTemplate'))
const ClassicSportTemplate = lazy(() => import('./ClassicSportTemplate'))

interface LazyTemplatePreviewProps extends Omit<BaseLayoutProps, 'config'> {
  templateId: string
}

const TemplatePreviewSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-48 w-full rounded-lg" />
    <div className="grid grid-cols-2 gap-4">
      <Skeleton className="h-32 w-full rounded-lg" />
      <Skeleton className="h-32 w-full rounded-lg" />
    </div>
    <div className="grid grid-cols-2 gap-4">
      <Skeleton className="h-40 w-full rounded-lg" />
      <Skeleton className="h-40 w-full rounded-lg" />
    </div>
    <div className="grid grid-cols-2 gap-4">
      <Skeleton className="h-32 w-full rounded-lg" />
      <Skeleton className="h-32 w-full rounded-lg" />
    </div>
  </div>
)

const LazyTemplatePreview: React.FC<LazyTemplatePreviewProps> = ({
  templateId,
  pageInfo,
  previewMode,
  className,
}) => {
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)

  const renderTemplate = () => {
    const props = { config, pageInfo, previewMode, className }

    switch (templateId) {
      case 'sport-modern':
        return <ModernSportTemplate {...props} />
      case 'sport-classic':
        return <ClassicSportTemplate {...props} />
      default:
        return (
          <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
            <p className="text-gray-500">Chưa chọn template</p>
          </div>
        )
    }
  }

  return (
    <Suspense fallback={<TemplatePreviewSkeleton />}>
      {renderTemplate()}
    </Suspense>
  )
}

export default LazyTemplatePreview
