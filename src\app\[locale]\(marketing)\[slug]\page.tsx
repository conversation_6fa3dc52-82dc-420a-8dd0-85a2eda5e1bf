import LoadingBase from '@/components/loading/loading-base'
import BookingPageNotAvailable from '@/modules/booking/components/BookingPageNotAvailable'
import { bookingPageService } from '@/modules/booking/services/booking-page.service'
import { getTranslations } from 'next-intl/server'
import dynamic from 'next/dynamic'
import { Suspense } from 'react'

// Lazy load PublicBookingPageV2 component
const PublicBookingPageV2 = dynamic(
  () => import('@/modules/booking/components/PublicBookingPageV2').then(mod => ({ default: mod.PublicBookingPageV2 })),
  {
    loading: LoadingBase,
    ssr: true, // Enable SSR for better SEO
  },
)

type Props = {
  params: {
    locale: string
    slug: string
  }
  searchParams: {
    isPreview?: string
  }
}

export async function generateMetadata({ params, searchParams }: Props) {
  const { slug, locale } = await params
  const { isPreview } = await searchParams

  try {
    // Use caching service to fetch data with isPreview parameter
    const { metadata } = await bookingPageService.fetchBookingPageWithMetadata(slug, isPreview === 'true')

    if (metadata.title && metadata.title !== `${slug} - PickSlot`) {
      return metadata
    }

    // Fallback to default metadata if booking page not found
    const t = await getTranslations({
      locale,
      namespace: 'Index',
    })

    return {
      title: t('meta_title'),
      description: t('meta_description'),
    }
  } catch (error: any) {
    // Fallback to default metadata if error occurs
    console.error('Error fetching booking page metadata:', error)
    const t = await getTranslations({
      locale,
      namespace: 'Index',
    })

    return {
      title: t('meta_title'),
      description: t('meta_description'),
    }
  }
}

export default async function BookingPageDetail({ params, searchParams }: Props) {
  try {
    const { slug } = await params
    const { isPreview } = await searchParams

    // Use caching service to fetch data with isPreview parameter
    const { bookingPage } = await bookingPageService.fetchBookingPageWithMetadata(slug, isPreview === 'true')

    if (bookingPage) {
      return (
        <Suspense
          fallback={<LoadingBase />}
        >
          <PublicBookingPageV2 bookingPage={bookingPage} isPreview={isPreview === 'true'} />
        </Suspense>
      )
    } else {
      // Hiển thị trang "Booking Page Not Available" thay vì notFound()
      return <BookingPageNotAvailable slug={slug} />
    }
  } catch (error: any) {
    // Nếu có lỗi khi lấy dữ liệu, hiển thị trang "Booking Page Not Available"
    console.error('Error fetching booking page:', error?.message || error)
    const { slug } = params
    return <BookingPageNotAvailable slug={slug} />
  }
}
