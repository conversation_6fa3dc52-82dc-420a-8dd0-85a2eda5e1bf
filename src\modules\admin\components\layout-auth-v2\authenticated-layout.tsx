import type { BookingPageItem } from '../../apis/booking-page.api'
import LayoutProvider from '@/components/layouts/layout-provider'
import { SidebarProvider } from '@/components/ui/sidebar'
import { cn } from '@/lib/utils'
import { AppSidebar } from './app-sidebar'

interface Props {
  children?: React.ReactNode
  bookingPageItem: BookingPageItem
}

export async function AuthenticatedLayout({ children, bookingPageItem }: Props) {
  return (
    <LayoutProvider>
      <SidebarProvider defaultOpen={true}>
        <AppSidebar bookingPageItem={bookingPageItem} />
        <div
          id="content"
          className={cn(
            'ml-auto w-full max-w-full',
            'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]',
            'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
            'sm:transition-[width] sm:duration-200 sm:ease-linear',
            'flex h-svh flex-col',
            'group-data-[scroll-locked=1]/body:h-full',
            'has-[main.fixed-main]:group-data-[scroll-locked=1]/body:h-svh',
          )}
        >
          {children}
        </div>
      </SidebarProvider>
    </LayoutProvider>
  )
}
