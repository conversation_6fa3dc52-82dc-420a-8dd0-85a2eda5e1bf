'use client'

import type { GridSlotSectionProps } from './types'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Card, CardContent } from '@/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { addMinutes, format, parse } from 'date-fns'
import { CalendarIcon, Clock } from 'lucide-react'
import React from 'react'
import { useCreateBookingPageV2Store } from '../../../stores/create-booking-page-v2.store'
import { useSelectedSlotsStore } from '../../../stores/selected-slots.store'

const TableGridSlotSection: React.FC<GridSlotSectionProps> = ({
  className,
}) => {
  const config = useCreateBookingPageV2Store(state => state.bookingConfig)
  const slotDuration = useCreateBookingPageV2Store(state => state.bookingConfig?.slotDuration || 60)
  const isMobile = false

  // Use store instead of local state
  const {
    toggleSlot,
    getFieldSlots,
    setSelectedDate,
    selectedDate: storeSelectedDate,
    getSlotStatus: getSlotStatusFromStore,
    isLoadingBookedSlots,
  } = useSelectedSlotsStore()

  // Use store selectedDate as primary, fallback to props
  const currentSelectedDate = storeSelectedDate

  // Initialize store with props selectedDate if store is empty
  React.useEffect(() => {
    if (!storeSelectedDate) {
      setSelectedDate(new Date())
    }
  }, [storeSelectedDate, setSelectedDate])

  // Generate time slots based on config open/close time and slot duration
  const generateTimeSlots = () => {
    const slots = []
    const startTime = config?.businessHours?.start || '00:00'
    const endTime = config?.businessHours?.end || '00:00'

    // Use date-fns to parse times properly
    const dayStart = parse(startTime, 'HH:mm', currentSelectedDate)
    const dayEnd = parse(endTime, 'HH:mm', currentSelectedDate)

    let currentSlot = dayStart
    while (currentSlot < dayEnd) {
      const timeStr = format(currentSlot, 'HH:mm')
      slots.push(timeStr)
      currentSlot = addMinutes(currentSlot, slotDuration) // Use configurable slot duration
    }

    return slots
  }

  const timeSlots = generateTimeSlots()

  const handleSlotClick = (fieldId: string, timeSlot: string) => {
    toggleSlot(fieldId, timeSlot)
  }

  const getSlotStatus = (fieldId: string, timeSlot: string) => {
    // Use store's getSlotStatus which handles both selected and booked slots
    return getSlotStatusFromStore(fieldId, timeSlot)
  }

  // Cải thiện màu sắc cho các trạng thái
  const getCellClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 cursor-not-allowed shadow-sm border-yellow-300 transition-colors duration-200'
      case 'confirmed':
        return 'bg-red-500 text-white cursor-not-allowed shadow-sm border-red-600 transition-colors duration-200'
      case 'cancelled':
        return 'bg-gray-100 text-gray-500 cursor-not-allowed shadow-sm border-gray-300 transition-colors duration-200'
      case 'selected':
        return 'bg-emerald-500 text-white cursor-pointer shadow-md border-emerald-600 hover:bg-emerald-600 hover:shadow-lg transition-all duration-200 transform'
      case 'available':
      default:
        return 'bg-gradient-to-br from-slate-50 to-slate-100 text-slate-700 cursor-pointer border-slate-200 hover:from-slate-100 hover:to-slate-200 hover:border-slate-300 hover:shadow-md transition-all duration-200 transform'
    }
  }

  const getFieldIcon = (fieldType: string) => {
    switch (fieldType) {
      case 'football':
        return '⚽'
      case 'tennis':
        return '🎾'
      case 'badminton':
        return '🏸'
      case 'basketball':
        return '🏀'
      default:
        return '🏟️'
    }
  }

  const renderTableGrid = () => {
    if (!config?.fields || config?.fields?.length === 0) {
      return (
        <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50">
          <CardContent className="p-8 text-center">
            <div className="text-amber-400 mb-2">
              <Clock className="w-12 h-12 mx-auto" />
            </div>
            <p className="text-slate-600 font-medium">
              Chưa có sân nào được cấu hình. Vui lòng thêm sân để hiển thị lịch đặt.
            </p>
          </CardContent>
        </Card>
      )
    }

    return (
      <div className="overflow-x-auto rounded-lg shadow-lg border border-slate-200">
        <table className="w-full border-collapse">
          {/* Header */}
          <thead>
            <tr className="bg-gradient-to-r from-blue-600 to-indigo-700">
              {config?.fields.map(field => (
                <th
                  key={field.id}
                  className="p-4 border-b border-blue-500 text-center font-bold text-white shadow-sm"
                >
                  <div className="flex flex-row justify-center items-center gap-2">
                    <span className="text-xl drop-shadow-sm">{getFieldIcon(field.type)}</span>
                    <span className={cn(
                      'font-semibold',
                      isMobile ? 'text-xs' : 'text-sm',
                    )}
                    >
                      {field.name}
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>

          {/* Body */}
          <tbody className="bg-white">
            {timeSlots.map((timeSlot, index) => (
              <tr
                key={timeSlot}
                className={cn(
                  index % 2 === 0 ? 'bg-white' : 'bg-slate-50/50',
                )}
              >
                {/* Field columns */}
                {config.fields.map((field) => {
                  const status = getSlotStatus(field.id, timeSlot)
                  const isDisabled = status === 'pending' || status === 'confirmed'

                  return (
                    <td
                      key={`${field.id}-${timeSlot}`}
                      className={cn(
                        'p-3 border-r border-slate-200 text-center relative',
                        getCellClass(status),
                        isMobile ? 'h-14' : 'h-16',
                        'last:border-r-0',
                      )}
                      onClick={() => !isDisabled && handleSlotClick(field.id, timeSlot)}
                    >
                      <div className="flex flex-col items-center justify-center h-full">
                        <span className={cn(
                          'font-semibold tracking-wide',
                          isMobile ? 'text-xs' : 'text-sm',
                          status === 'selected' && 'text-white',
                          (status === 'pending' || status === 'confirmed') && 'text-white',
                          status === 'available' && 'text-slate-700',
                        )}
                        >
                          {timeSlot}
                        </span>
                      </div>
                    </td>
                  )
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6 py-4', className)}>
      <div className={cn(
        'flex items-center justify-between',
        isMobile ? 'flex-col gap-4' : 'flex-row',
      )}
      >
        <h2 className={cn(
          'font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Lịch đặt sân
        </h2>

        {/* Date Picker */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'justify-start text-left font-normal bg-white border-slate-300 hover:bg-slate-50 hover:border-slate-400 shadow-sm',
                isMobile ? 'w-full' : 'w-[240px]',
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4 text-slate-500" />
              {currentSelectedDate
                ? (
                    currentSelectedDate.toLocaleDateString('vi-VN', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })
                  )
                : (
                    <span className="text-slate-500">Chọn ngày</span>
                  )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 shadow-xl border-slate-200" align="end">
            <Calendar
              mode="single"
              selected={currentSelectedDate}
              onSelect={(date) => {
                if (date) {
                  setSelectedDate(date)
                }
              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Legend */}
      <div className={cn(
        'flex gap-6 p-4 bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl border border-slate-200 shadow-sm',
        isMobile ? 'flex-wrap' : 'flex-row',
      )}
      >
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-slate-300 rounded-md shadow-sm"></div>
          <span className={cn('text-slate-700 font-medium', isMobile ? 'text-xs' : 'text-sm')}>
            Có sẵn
          </span>
        </div>
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-md shadow-sm"></div>
          <span className={cn('text-slate-700 font-medium', isMobile ? 'text-xs' : 'text-sm')}>
            Đã chọn
          </span>
        </div>
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-gradient-to-br from-red-400 to-red-500 rounded-md shadow-sm"></div>
          <span className={cn('text-slate-700 font-medium', isMobile ? 'text-xs' : 'text-sm')}>
            Đã đặt
          </span>
        </div>
      </div>

      {/* Table Grid */}
      {renderTableGrid()}
    </div>
  )
}

export default TableGridSlotSection
