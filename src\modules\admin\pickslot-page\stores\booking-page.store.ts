import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface BookingPageState {
  bookingPage: BookingPageItem | null
  isLoading: boolean
  error: string | null
  fetchBookingPage: (_id: string) => Promise<void>
  setBookingPage: (_: BookingPageItem | null) => void
  reset: () => void
}

export const usePickslotPageStore = create<BookingPageState>()(
  devtools(
    immer(set => ({
      bookingPage: null,
      isLoading: false,
      error: null,
      fetchBookingPage: async (id: string) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })
        try {
          const res = await bookingPageAPIs.getBookingPageById(id)
          set((state) => {
            state.bookingPage = res.data || null
            state.isLoading = false
          })
        } catch (err: any) {
          set((state) => {
            state.error = err?.message || 'Failed to fetch booking page'
            state.isLoading = false
          })
        }
      },
      setBookingPage: bookingPage =>
        set((state) => {
          state.bookingPage = bookingPage
        }),
      reset: () =>
        set((state) => {
          state.bookingPage = null
          state.isLoading = false
          state.error = null
        }),
    })),
  ),
)

export const usePickslotPage = () => usePickslotPageStore(state => state.bookingPage)
export const usePickslotPageLoading = () => usePickslotPageStore(state => state.isLoading)
export const usePickslotPageError = () => usePickslotPageStore(state => state.error)
export const useFetchPickslotPage = () => usePickslotPageStore(state => state.fetchBookingPage)
export const useSetPickslotPage = () => usePickslotPageStore(state => state.setBookingPage)
export const useResetPickslotPage = () => usePickslotPageStore(state => state.reset)
