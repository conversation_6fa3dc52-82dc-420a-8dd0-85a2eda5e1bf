import type { LucideIcon } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import { useNavigation } from '@/hooks/useNavigation'
import { useFetchUserProfile, useUser, useUserLoading } from '@/modules/user/stores/user.store'
import { clearAuthTokens } from '@/services/auth'
import { appPaths } from '@/utils/app-routes'
import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  Sparkles,
} from 'lucide-react'
import Link from 'next/link'
import React from 'react'

interface MenuItem {
  label: string
  icon: LucideIcon
  href?: string
  onClick?: () => void
  divider?: boolean
}

interface MenuSection {
  title?: string
  items: MenuItem[]
}

export function NavUser() {
  const { isMobile } = useSidebar()
  const { navigate } = useNavigation()
  const userData = useUser()
  const fetchProfile = useFetchUserProfile()
  const isLoading = useUserLoading()

  // Use either the provided user or the fetched user data
  const user = userData

  React.useEffect(() => {
    if (!user) {
      fetchProfile()
    }
  }, [user, fetchProfile])

  const handleLogout = () => {
    clearAuthTokens()
    navigate(appPaths.auth.login())
  }

  const menuSections: MenuSection[] = [
    {
      items: [
        {
          label: 'Upgrade to Pro',
          icon: Sparkles,
        },
      ],
    },
    {
      items: [
        {
          label: 'Account',
          icon: BadgeCheck,
          href: '/settings/account',
        },
        {
          label: 'Billing',
          icon: CreditCard,
          href: '/settings',
        },
        {
          label: 'Notifications',
          icon: Bell,
          href: '/settings/notifications',
        },
      ],
    },
    {
      items: [
        {
          label: 'Log out',
          icon: LogOut,
          onClick: handleLogout,
        },
      ],
    },
  ]

  // Show loading state or default while fetching user data
  if (isLoading || !user) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" className="opacity-50">
            <Avatar className="h-8 w-8 rounded-lg">
              <AvatarFallback className="rounded-lg">...</AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">Loading...</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar || undefined} alt={user?.name || ''} />
                <AvatarFallback>{user?.name?.[0] || '?'}</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{isLoading ? '...' : (user?.name || '...')}</span>
                <span className="truncate text-xs">{isLoading ? '...' : (user?.email || '...')}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? 'bottom' : 'right'}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.avatar || undefined} alt={user?.name || ''} />
                  <AvatarFallback>{user?.name?.[0] || '?'}</AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{user.name}</span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />

            {menuSections.map((section, index) => (
              <React.Fragment key={`menu-section-${section.items[0]?.label || index}`}>
                <DropdownMenuGroup>
                  {section.items.map((item) => {
                    const ItemIcon = item.icon
                    if (item.href) {
                      return (
                        <DropdownMenuItem key={`item-${item.label}`} asChild>
                          <Link href={item.href}>
                            <ItemIcon />
                            {item.label}
                          </Link>
                        </DropdownMenuItem>
                      )
                    }
                    return (
                      <DropdownMenuItem key={`item-${item.label}`} onClick={item.onClick}>
                        <ItemIcon />
                        {item.label}
                      </DropdownMenuItem>
                    )
                  })}
                </DropdownMenuGroup>
                {index < menuSections.length - 1 && <DropdownMenuSeparator />}
              </React.Fragment>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
