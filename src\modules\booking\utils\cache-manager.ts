import { globalCache } from './booking-page-cache'

/**
 * Cache Manager utility for managing booking page cache
 * This can be used from anywhere in the application to manage cache
 */
export class CacheManager {
  /**
   * Invalidate cache for a specific slug
   * Useful when booking page data is updated
   */
  static invalidateBookingPage(slug: string): void {
    globalCache.remove(slug)
    console.warn(`[CacheManager] Invalidated cache for slug: ${slug}`)
  }

  /**
   * Invalidate cache for multiple slugs
   */
  static invalidateMultipleBookingPages(slugs: string[]): void {
    slugs.forEach(slug => this.invalidateBookingPage(slug))
    console.warn(`[CacheManager] Invalidated cache for ${slugs.length} slugs`)
  }

  /**
   * Clear all cache
   * Useful for maintenance or when switching users
   */
  static clearAllCache(): void {
    globalCache.clear()
    console.warn('[CacheManager] Cleared all cache')
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): { size: number } {
    return {
      size: globalCache.getSize(),
    }
  }

  /**
   * Check if a slug is cached
   */
  static isCached(slug: string): boolean {
    return globalCache.has(slug)
  }

  /**
   * Get cached data for a slug (if available)
   */
  static getCachedData(slug: string) {
    return globalCache.get(slug)
  }

  /**
   * Set custom TTL for specific slugs
   * Useful for frequently changing data
   */
  static setCustomTTL(slug: string, ttl: number): void {
    const existingData = globalCache.get(slug)
    if (existingData) {
      // Re-cache with new TTL
      globalCache.set(slug, existingData, ttl)
      console.warn(`[CacheManager] Updated TTL for slug: ${slug} to ${ttl}ms`)
    }
  }

  /**
   * Pre-warm cache for specific slugs
   * Useful for critical pages that should be fast
   */
  static async preWarmCache(slugs: string[], service: any): Promise<void> {
    console.warn(`[CacheManager] Pre-warming cache for ${slugs.length} slugs`)

    const promises = slugs.map(async (slug) => {
      try {
        await service.fetchBookingPage({ slug, forceRefresh: true })
      } catch (error) {
        console.warn(`[CacheManager] Failed to pre-warm cache for slug: ${slug}`, error)
      }
    })

    await Promise.allSettled(promises)
    console.warn('[CacheManager] Pre-warming completed')
  }
}

// Export singleton instance
export const cacheManager = CacheManager
