'use client'

import dynamic from 'next/dynamic'
import { useParams } from 'next/navigation'

const EditBookingPageV2Screen = dynamic(() => import('@/modules/admin/create-booking-page-v2/screens/EditBookingPageV2Screen'), {
  loading: () => <p>Loading...</p>,
  ssr: false,
})

/**
 * Trang chỉnh sửa booking page với giao diện V2
 */
export default function EditBookingPageV2Page() {
  const params = useParams()
  const bookingPageId = params.pageId as string

  return (
    <>
      <EditBookingPageV2Screen bookingPageId={bookingPageId} />
    </>
  )
}
