import type { BookingPageItem } from '../../apis/booking-page.api'
import type { BookingPageTemplate } from '../constants/templates'
import type { Block, Template } from '../types/blocks'
import { create } from 'zustand'
import { DEFAULT_EVENT_BLOCKS } from '../constants/defaultBlocks'
import { BOOKING_PAGE_TEMPLATES } from '../constants/templates'
import { LayoutType } from '../types/theme'

// Booking page information
export interface BookingPageInfo {
  name: string
  description: string
  slug: string
}

export enum BookingPageMode {
  CREATE = 'create',
  EDIT = 'edit',
}

export interface TemplateConfigData {
  defaultData?: BookingPageItem

  templateSelected: BookingPageTemplate
  template: Template
  blocks: Block[]
  themeSettings: {
    primaryColor: string
    fontFamily: string
    layout: LayoutType
  }
  pageInfo: BookingPageInfo

  mode: BookingPageMode
}

type State = TemplateConfigData & {
  // Actions
  setTemplateSelected: (_data: BookingPageTemplate) => void
  setTemplate: (_template: Template) => void
  setBlocks: (_blocks: Block[]) => void
  updateBlock: (_index: number, _block: Block) => void
  addBlock: (_blockType: Block['type']) => void
  removeBlock: (_index: number) => void
  reorderBlocks: (_sourceIndex: number, _destinationIndex: number) => void
  setThemeSettings: (_settings: Partial<State['themeSettings']>) => void
  setPageInfo: (_info: Partial<BookingPageInfo>) => void
  setInitData: (_data: BookingPageItem) => void
}

// Create default template
const defaultTemplate: Template = {
  id: 'event-booking',
  name: 'Event Booking',
  templateCode: 'EVENT_BOOKING',
  description: 'Template for event booking pages',
  image: 'https://placehold.co/200x120?text=Event+Booking',
  blocks: DEFAULT_EVENT_BLOCKS,
  theme: {
    primaryColor: '#2563eb',
    fontFamily: 'Inter, sans-serif',
    layout: LayoutType.VERTICAL,
  },
}

const defaultConfigData: TemplateConfigData = {
  templateSelected: BOOKING_PAGE_TEMPLATES[0]!,
  template: defaultTemplate,
  blocks: DEFAULT_EVENT_BLOCKS,
  themeSettings: {
    primaryColor: '#2563eb',
    fontFamily: 'Inter, sans-serif',
    layout: LayoutType.VERTICAL,
  },
  pageInfo: {
    name: '',
    description: '',
    slug: '',
  },
  mode: BookingPageMode.CREATE,
}

export const useBookingPageConfigStore = create<State>()(set => ({
  // State initialization
  ...defaultConfigData,

  get getDefaultData() {
    return this.defaultData
  },

  // Actions
  setTemplateSelected: data => set((_) => {
    // Since we're now using BookingPageTemplate which already has blocks and theme
    // We can directly use it as our template

    // Update both templateSelected and template
    return {
      templateSelected: data,
      template: data,
      blocks: data.blocks,
      themeSettings: data.theme
        ? {
            primaryColor: data.theme.primaryColor,
            fontFamily: data.theme.fontFamily,
            layout: data.theme.layout,
          }
        : _.themeSettings,
    }
  }),

  // Block-based actions
  setTemplate: template => set(_ => ({
    template,
    blocks: template.blocks,
    themeSettings: template.theme
      ? {
          primaryColor: template.theme.primaryColor,
          fontFamily: template.theme.fontFamily,
          layout: template.theme.layout,
        }
      : _.themeSettings,
  })),

  setBlocks: blocks => set(_ => ({ blocks })),

  updateBlock: (index, block) => set(state => ({
    blocks: state.blocks.map((b, i) => i === index ? block : b),
  })),

  addBlock: blockType => set((state) => {
    // Create a new block with default data based on type
    const newBlock = createDefaultBlock(blockType)
    return { blocks: [...state.blocks, newBlock] }
  }),

  removeBlock: index => set(state => ({
    blocks: state.blocks.filter((_, i) => i !== index),
  })),

  reorderBlocks: (sourceIndex, destinationIndex) => set((state) => {
    const newBlocks = [...state.blocks]
    const [removed] = newBlocks.splice(sourceIndex, 1)
    if (removed) {
      newBlocks.splice(destinationIndex, 0, removed)
    }
    return { blocks: newBlocks }
  }),

  setThemeSettings: settings => set(state => ({
    themeSettings: { ...state.themeSettings, ...settings },
  })),

  setPageInfo: info => set(state => ({
    pageInfo: { ...state.pageInfo, ...info },
  })),

  // set init data
  setInitData: data => set((_) => {
    const template = BOOKING_PAGE_TEMPLATES.find(t => t.templateCode === data?.templateCode)

    return ({
      defaultData: data,

      templateSelected: template!,
      template: template!,
      blocks: data.blocks,
      themeSettings: data.theme,
      pageInfo: {
        name: data.name,
        description: data.description,
        slug: data.slug,
      },
      mode: BookingPageMode.EDIT,
    })
  }),
}))

// Helper function to create a default block based on type
function createDefaultBlock(type: Block['type']): Block {
  switch (type) {
    case 'banner':
      return {
        type: 'banner',
        data: {
          image: 'https://placehold.co/800x400?text=Banner+Image',
          title: 'Event Title',
          subtitle: 'Event Subtitle',
        },
      }

    case 'info':
      return {
        type: 'info',
        data: {
          time: 'DD/MM/YYYY HH:MM',
          location: 'Event Location',
          slots: 10,
          price: 100000,
        },
      }

    case 'description':
      return {
        type: 'description',
        data: {
          content: 'Enter your event description here.',
        },
      }

      // case 'booking_form':
      //   return {
      //     type: 'booking_form',
      //     data: {
      //       fields: ['name', 'email'],
      //       payment_methods: ['COD'],
      //     },
      //   }

    case 'map':
      return {
        type: 'map',
        data: {
          iframe: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.4946681007846!2d106.69908367469967!3d10.771913089387625!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31752f4b3330bcc7%3A0x4db964d76bf6e18e!2sIndependence%20Palace!5e0!3m2!1sen!2s!4v1689612010894!5m2!1sen!2s',
        },
      }

    case 'availability_calendar':
      return {
        type: 'availability_calendar',
        data: {
          title: 'Đặt sân',
          subtitle: 'Chọn ngày và giờ phù hợp với bạn',
          view: 'month',
          defaultView: 'month',
          businessHours: {
            start: '08:00',
            end: '20:00',
            daysOfWeek: [1, 2, 3, 4, 5, 6],
          },
          showWeekends: true,
          firstDayOfWeek: 1,
          timeSlotInterval: 60,
          showAvailabilityLegend: true,
          fields: [
            {
              id: 'field-1',
              name: 'Sân 1',
              type: 'football',
              capacity: 1,
            },
            {
              id: 'field-2',
              name: 'Sân 2',
              type: 'football',
              capacity: 1,
            },
            {
              id: 'field-3',
              name: 'Sân 3',
              type: 'football',
              capacity: 1,
            },
            {
              id: 'field-4',
              name: 'Sân 4',
              type: 'tennis',
              capacity: 1,
            },
          ],
          configMode: 'common',
          commonConfig: {
            pricePerHour: 300000,
            dynamicPricing: {
              enabled: false,
              timeBasedPrices: [],
              dayBasedPrices: [],
              combinedRules: [],
            },
          },
        },
      }

    case 'booking_ticket':
      return {
        type: 'booking_ticket',
        data: {
          title: 'Đặt vé',
          subtitle: 'Hoàn tất thông tin đặt vé của bạn',
          showPriceBreakdown: true,
          showBookingSummary: true,
          ctaButtonText: 'Đặt vé ngay',
          fields: ['name', 'email', 'phone'],
          paymentMethods: ['COD'],
        },
      }

    default:
      return {
        type: 'description',
        data: {
          content: 'Default block content',
        },
      }
  }
}
