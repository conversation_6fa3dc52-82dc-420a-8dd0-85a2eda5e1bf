import type { ModalManagerRef } from './modal-manager'
import type { AlertModalData, ConfirmModalData, LoadingModalData } from './modal-store'
import { createRef } from 'react'

// Create a global ref for the modal manager
// eslint-disable-next-line react/no-create-ref
export const modalManagerRef = createRef<ModalManagerRef>()

// Helper function to ensure modal manager is available
const getModalManager = () => {
  if (!modalManagerRef.current) {
    console.warn('Modal Manager is not initialized. Make sure ModalManager component is rendered.')
    return null
  }
  return modalManagerRef.current
}

// Global modal API functions
export const modalAPI = {
  /**
   * Show loading modal
   */
  showLoading: (options?: Partial<Omit<LoadingModalData, 'type' | 'id'>>) => {
    const manager = getModalManager()
    return manager?.showLoading(options) || ''
  },

  /**
   * Show confirm modal
   */
  showConfirm: (options: Omit<ConfirmModalData, 'type' | 'id'>) => {
    const manager = getModalManager()
    return manager?.showConfirm(options) || ''
  },

  /**
   * Show alert modal
   */
  showAlert: (options: Omit<AlertModalData, 'type' | 'id'>) => {
    const manager = getModalManager()
    return manager?.showAlert(options) || ''
  },

  /**
   * Show success alert
   */
  showSuccess: (message: string, title = 'Success') => {
    const manager = getModalManager()
    return manager?.showSuccess(message, title) || ''
  },

  /**
   * Show error alert
   */
  showError: (message: string, title = 'Error') => {
    const manager = getModalManager()
    return manager?.showError(message, title) || ''
  },

  /**
   * Show warning alert
   */
  showWarning: (message: string, title = 'Warning') => {
    const manager = getModalManager()
    return manager?.showWarning(message, title) || ''
  },

  /**
   * Show delete confirmation
   */
  showDeleteConfirm: (
    onConfirm: () => void | Promise<void>,
    itemName = 'this item',
  ) => {
    const manager = getModalManager()
    return manager?.showDeleteConfirm(onConfirm, itemName) || ''
  },

  /**
   * Close specific modal
   */
  closeModal: (id: string) => {
    const manager = getModalManager()
    manager?.closeModal(id)
  },

  /**
   * Close all modals
   */
  closeAllModals: () => {
    const manager = getModalManager()
    manager?.closeAllModals()
  },

  /**
   * Update modal content
   */
  updateModal: (id: string, updates: any) => {
    const manager = getModalManager()
    manager?.updateModal(id, updates)
  },
}

// Export individual functions for convenience
export const {
  showLoading,
  showConfirm,
  showAlert,
  showSuccess,
  showError,
  showWarning,
  showDeleteConfirm,
  closeModal,
  closeAllModals,
  updateModal,
} = modalAPI
