'use client'

import type { CreateBookingPagePayload } from '../../apis/booking-page.api'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, Settings } from 'lucide-react'
import React, { useState } from 'react'
import { toast } from 'sonner'
import { bookingPageAPIs } from '../../apis/booking-page.api'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

interface UpdateButtonProps {
  bookingPageId: string
  className?: string
}

export const UpdateButton: React.FC<UpdateButtonProps> = ({
  bookingPageId,
  className = 'bg-green-500 hover:bg-green-600 text-white px-6 h-10',
}) => {
  const [updating, setUpdating] = useState(false)

  // Get data from store
  const currentStep = useCreateBookingPageV2Store(state => state.currentStep)
  const pageInfo = useCreateBookingPageV2Store(state => state.pageInfo)
  const selectedTemplateId = useCreateBookingPageV2Store(state => state.selectedTemplateId)
  const bookingConfig = useCreateBookingPageV2Store(state => state.bookingConfig)
  const setCurrentStep = useCreateBookingPageV2Store(state => state.setCurrentStep)
  const nextStep = useCreateBookingPageV2Store(state => state.nextStep)

  const handleUpdate = async () => {
    if (!bookingPageId) {
      return
    }

    setUpdating(true)
    try {
      const payload: CreateBookingPagePayload = {
        name: pageInfo.name,
        description: pageInfo.description,
        slug: pageInfo.slug,
        templateCode: selectedTemplateId,
        blocks: [{
          type: 'config',
          data: {
            ...bookingConfig,
          },
        }],
        theme: {
          primaryColor: '#FF5722',
          fontFamily: 'Roboto',
          layout: 'default' as any,
        },
      }

      const response = await bookingPageAPIs.updateBookingPage(bookingPageId, payload)

      if (response?.status?.success) {
        toast.success('Cập nhật trang PickSlot thành công!')
      } else {
        // Xử lý lỗi slug đã tồn tại
        if (response?.status?.code === 'booking-page/slug-exists') {
          toast.error('Slug đã tồn tại, vui lòng chọn slug khác!')
        } else {
          toast.error(response?.status?.message || 'Có lỗi xảy ra khi cập nhật')
        }
      }
    } catch (err: any) {
      toast.error(err?.message || 'Có lỗi khi cập nhật trang đặt chỗ')
    } finally {
      setUpdating(false)
    }
  }

  const handleBackToTemplate = () => {
    setCurrentStep(2)
    toast.info('Đã chuyển về bước chọn template')
  }

  const handleNextToConfig = () => {
    nextStep()
    toast.info('Đã chuyển đến bước thiết lập')
  }

  // Kiểm tra xem có đang ở bước 2 và đã chọn template chưa
  const isAtTemplateStep = currentStep === 2
  const hasSelectedTemplate = Boolean(selectedTemplateId && selectedTemplateId.trim() !== '')

  return (
    <div className="flex items-center gap-3">
      {isAtTemplateStep && hasSelectedTemplate
        ? (
            <Button
              onClick={handleNextToConfig}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 h-10"
            >
              <ChevronRight className="w-4 h-4 mr-2" />
              Tới bước config
            </Button>
          )
        : (
            <Button
              onClick={handleBackToTemplate}
              variant="outline"
              className="border-orange-300 text-orange-600 hover:bg-orange-50 px-4 h-10"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Chọn Template
            </Button>
          )}

      <Button
        onClick={handleUpdate}
        disabled={updating || (isAtTemplateStep && hasSelectedTemplate)}
        className={className}
      >
        {updating
          ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Đang cập nhật...
              </>
            )
          : (
              <>
                <Settings className="w-4 h-4 mr-2" />
                Cập nhật
              </>
            )}
      </Button>
    </div>
  )
}
