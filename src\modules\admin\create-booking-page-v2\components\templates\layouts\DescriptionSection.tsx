'use client'

import type { DescriptionSectionProps } from './types'
import { cn } from '@/lib/utils'
import { Clock, MapPin } from 'lucide-react'
import React from 'react'
import { useCreateBookingPageV2Store } from '../../../stores/create-booking-page-v2.store'

const DescriptionSection: React.FC<DescriptionSectionProps> = ({ className }) => {
  const config = useCreateBookingPageV2Store(state => state.bookingConfig?.description)
  const businessHours = useCreateBookingPageV2Store(state => state.bookingConfig?.businessHours)

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 p-6',
      className,
    )}
    >
      <div className="space-y-6">
        {/* Description Section */}
        <div className="space-y-4">
          <div>
            <h2 className={cn(
              'font-bold text-gray-900 mb-3',
              'text-xl',
            )}
            >
              Thông tin sân
            </h2>

            {config?.content && (
              <p className={cn(
                'text-gray-600 leading-relaxed break-words',
                'text-base',
              )}
              >
                {config?.content}
              </p>
            )}
          </div>

          {/* Info Items */}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Clock className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                'text-base',
              )}
              >
                Giờ hoạt động:
                {' '}
                {businessHours?.start || '06:00'}
                {' '}
                -
                {' '}
                {businessHours?.end || '22:00'}
              </span>
            </div>

            {config?.location && (
              <div className="flex items-center gap-3">
                <MapPin className="w-5 h-5 text-orange-500 flex-shrink-0" />
                <span className={cn(
                  'text-gray-700',
                  'text-base',
                )}
                >
                  { config?.location }
                </span>
              </div>
            )}

            {/* <div className="flex items-center gap-3">
              <Star className="w-5 h-5 text-orange-500 flex-shrink-0" />
              <span className={cn(
                'text-gray-700',
                isMobile ? 'text-sm' : 'text-base',
              )}
              >
                Đánh giá: 4.8/5 (128 đánh giá)
              </span>
            </div> */}
          </div>
        </div>

      </div>
    </div>
  )
}

export default DescriptionSection
