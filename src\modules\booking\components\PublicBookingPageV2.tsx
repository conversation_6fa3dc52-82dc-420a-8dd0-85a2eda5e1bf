'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import LoadingBase from '@/components/loading/loading-base'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2'
import LazyTemplatePreview from '@/modules/admin/create-booking-page-v2/components/templates/LazyTemplatePreview'
import React from 'react'
import TriggerManager from './TriggerManager'

interface PublicBookingPageV2Props {
  bookingPage: BookingPageItem
  isPreview?: boolean
}

export const PublicBookingPageV2: React.FC<PublicBookingPageV2Props> = ({ bookingPage, isPreview = false }) => {
  const initializeFromBookingPage = useCreateBookingPageV2Store(state => state.initializeFromBookingPage)
  const setIsPreview = useCreateBookingPageV2Store(state => state.setIsPreview)
  const [initialized, setInitialized] = React.useState(false)

  React.useEffect(() => {
    if (bookingPage && !initialized) {
      initializeFromBookingPage(bookingPage, 3)
      // Set preview mode based on isPreview prop
      setIsPreview(isPreview)
      // Use a microtask to avoid calling setInitialized directly in useEffect
      Promise.resolve().then(() => setInitialized(true))
    }
  }, [bookingPage, initializeFromBookingPage, isPreview, setIsPreview])

  if (!initialized) {
    return <LoadingBase />
  }

  return (
    <div>
      <TriggerManager bookingPageId={bookingPage._id} />

      <LazyTemplatePreview
        templateId={bookingPage.templateCode}
      />
    </div>
  )
}
