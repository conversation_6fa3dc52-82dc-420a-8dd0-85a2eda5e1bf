'use client'

import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import LoadingBase from '@/components/loading/loading-base'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2'
import LazyTemplatePreview from '@/modules/admin/create-booking-page-v2/components/templates/LazyTemplatePreview'
import React from 'react'

interface PublicBookingPageV2Props {
  bookingPage: BookingPageItem
  isPreview?: boolean
}

export const PublicBookingPageV2: React.FC<PublicBookingPageV2Props> = ({ bookingPage, isPreview = false }) => {
  const initializeFromBookingPage = useCreateBookingPageV2Store(state => state.initializeFromBookingPage)
  const setIsPublished = useCreateBookingPageV2Store(state => state.setIsPublished)
  const [initialized, setInitialized] = React.useState(false)

  React.useEffect(() => {
    if (bookingPage && !initialized) {
      initializeFromBookingPage(bookingPage, 3)
      if (!isPreview) {
        setIsPublished(true)
      }
      // Use a microtask to avoid calling setInitialized directly in useEffect
      Promise.resolve().then(() => setInitialized(true))
    }
  }, [bookingPage, initializeFromBookingPage])

  if (!initialized) {
    return <LoadingBase />
  }

  return (
    <div>
      <LazyTemplatePreview
        templateId={bookingPage.templateCode}
      />
    </div>
  )
}
