'use client'

import type { AlertModalData, ConfirmModalData, LoadingModalData } from './modal-store'
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import { createPortal } from 'react-dom'
import { modalManagerRef } from './modal-api'
import { useModalStore } from './modal-store'
import { AlertModal } from './modals/alert-modal'
import { ConfirmModal } from './modals/confirm-modal'
import { LoadingModal } from './modals/loading-modal'

export interface ModalManagerRef {
  showLoading: (_options?: Partial<Omit<LoadingModalData, 'type' | 'id'>>) => string
  showConfirm: (_options: Omit<ConfirmModalData, 'type' | 'id'>) => string
  showAlert: (_options: Omit<AlertModalData, 'type' | 'id'>) => string
  showSuccess: (_message: string, _title?: string) => string
  showError: (_message: string, _title?: string) => string
  showWarning: (_message: string, _title?: string) => string
  showDeleteConfirm: (_onConfirm: () => void | Promise<void>, _itemName?: string) => string
  closeModal: (_id: string) => void
  closeAllModals: () => void
  updateModal: (_id: string, _updates: any) => void
}

export const ModalManager = forwardRef<ModalManagerRef>((_props, _) => {
  const [mounted, setMounted] = useState(false)
  const { modals, openModal, closeModal, closeAllModals, updateModal } = useModalStore()

  useEffect(() => {
    setMounted(true)
  }, [])

  useImperativeHandle(modalManagerRef, () => ({
    showLoading: (options?: Partial<Omit<LoadingModalData, 'type' | 'id'>>) => {
      return openModal({
        type: 'loading',
        title: 'Loading...',
        message: 'Please wait while we process your request.',
        showSpinner: true,
        allowClose: false,
        ...options,
      })
    },

    showConfirm: (options: Omit<ConfirmModalData, 'type' | 'id'>) => {
      return openModal({
        type: 'confirm',
        title: 'Confirm Action',
        description: 'Are you sure you want to proceed?',
        confirmText: 'Confirm',
        cancelText: 'Cancel',
        variant: 'default',
        ...options,
      })
    },

    showAlert: (options: Omit<AlertModalData, 'type' | 'id'>) => {
      return openModal({
        type: 'alert',
        title: 'Alert',
        description: 'This is an alert message.',
        okText: 'OK',
        variant: 'default',
        ...options,
      })
    },

    showSuccess: (message: string, title = 'Success') => {
      return openModal({
        type: 'alert',
        title,
        description: message,
        variant: 'success',
      })
    },

    showError: (message: string, title = 'Error') => {
      return openModal({
        type: 'alert',
        title,
        description: message,
        variant: 'destructive',
      })
    },

    showWarning: (message: string, title = 'Warning') => {
      return openModal({
        type: 'alert',
        title,
        description: message,
        variant: 'warning',
      })
    },

    showDeleteConfirm: (
      onConfirm: () => void | Promise<void>,
      itemName = 'this item',
    ) => {
      return openModal({
        type: 'confirm',
        title: 'Delete Confirmation',
        description: `Are you sure you want to delete ${itemName}? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        variant: 'destructive',
        onConfirm,
      } as ConfirmModalData)
    },

    closeModal,
    closeAllModals,
    updateModal,
  }), [openModal, closeModal, closeAllModals, updateModal])

  if (!mounted) {
    return null
  }

  const modalElements = modals.map((modal) => {
    let ModalComponent = (_: any) => <></>

    switch (modal.type) {
      case 'loading':
        ModalComponent = LoadingModal
        break
      case 'confirm':
        ModalComponent = ConfirmModal
        break
      case 'alert':
        ModalComponent = AlertModal
        break
      default:
        return null
    }

    return (
      <ModalComponent
        key={modal.id}
        {...modal}
        onClose={() => closeModal(modal.id)}
      />
    )
  })

  if (modalElements.length === 0) {
    return null
  }

  return createPortal(
    <div className="modal-manager-container">
      {modalElements}
    </div>,
    document.body,
  )
})
