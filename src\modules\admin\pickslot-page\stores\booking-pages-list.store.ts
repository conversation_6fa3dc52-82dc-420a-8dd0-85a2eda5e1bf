import type { BookingPageItem } from '@/modules/admin/apis/booking-page.api'
import { bookingPageAPIs } from '@/modules/admin/apis/booking-page.api'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

interface BookingPagesState {
  bookingPages: BookingPageItem[]
  isLoading: boolean
  error: string | null
  fetchBookingPages: () => Promise<void>
  setBookingPages: (_: BookingPageItem[]) => void
  reset: () => void
}

export const useBookingPagesStore = create<BookingPagesState>()(
  devtools(
    immer(set => ({
      bookingPages: [],
      isLoading: false,
      error: null,
      fetchBookingPages: async () => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })
        try {
          const res = await bookingPageAPIs.getBookingPages()
          set((state) => {
            state.bookingPages = res.data || []
            state.isLoading = false
          })
        } catch (err: any) {
          set((state) => {
            state.error = err?.message || 'Failed to fetch booking pages'
            state.isLoading = false
          })
        }
      },
      setBookingPages: bookingPages =>
        set((state) => {
          state.bookingPages = bookingPages
        }),
      reset: () =>
        set((state) => {
          state.bookingPages = []
          state.isLoading = false
          state.error = null
        }),
    })),
  ),
)

// Hook exports for easy access
export const useBookingPages = () => useBookingPagesStore(state => state.bookingPages)
export const useBookingPagesLoading = () => useBookingPagesStore(state => state.isLoading)
export const useBookingPagesError = () => useBookingPagesStore(state => state.error)
export const useFetchBookingPages = () => useBookingPagesStore(state => state.fetchBookingPages)
export const useSetBookingPages = () => useBookingPagesStore(state => state.setBookingPages)
export const useResetBookingPages = () => useBookingPagesStore(state => state.reset)
