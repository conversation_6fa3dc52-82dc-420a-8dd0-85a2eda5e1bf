name: 🚀 Deploy Next.js to VPS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy to VPS
        run: |
          ssh ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} << 'EOF'
            mkdir -p /var/www/app-booking-easy-fe
            cd /var/www/app-booking-easy-fe

            if [ ! -d ".git" ]; then
              <NAME_EMAIL>:the-one-life/app-booking-easy-fe.git .
            else
              echo "Pulling latest changes..."
              git pull origin main
            fi

            npm install
            npm run build
            # pm2 restart next-app || pm2 start npm --name "next-app" -- start
          EOF
