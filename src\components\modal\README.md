# Modal Manager

M<PERSON><PERSON> hệ thống quản lý modal toàn cục có thể sử dụng ở bất kỳ đâu trong ứng dụng. Sử dụng Zustand để quản lý state, createPortal để render modal trong body, và useImperativeHandle + createRef để có thể gọi từ bất kỳ đâu mà không cần hook.

## Tính năng

- ✅ Quản lý modal toàn cục với Zustand
- ✅ Sử dụng createPortal để render trong body
- ✅ Imperative API với useImperativeHandle và createRef
- ✅ Có thể gọi từ bất kỳ đâu mà không cần hook
- ✅ Hỗ trợ nhiều loại modal: Loading, Confirm, Alert
- ✅ Có thể mở nhiều modal cùng lúc
- ✅ API đơn giản và dễ sử dụng
- ✅ TypeScript support đầy đủ
- ✅ Responsive design
- ✅ Dark mode support

## Cài đặt

### 1. Thêm ModalProvider vào layout chính

```tsx
// app/layout.tsx hoặc _app.tsx
import { ModalProvider } from '@/components/modal'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html>
      <body>
        {children}
        <ModalProvider />
      </body>
    </html>
  )
}
```

### 2. Sử dụng với Hook (trong React component)

```tsx
import { useModal } from '@/components/modal'

function MyComponent() {
  const modal = useModal()

  const handleSave = async () => {
    // Hiển thị loading
    const loadingId = modal.showLoading({
      title: 'Saving...',
      message: 'Please wait while we save your data.'
    })

    try {
      await saveData()
      modal.closeModal(loadingId)
      modal.showSuccess('Data saved successfully!')
    } catch (error) {
      modal.closeModal(loadingId)
      modal.showError('Failed to save data. Please try again.')
    }
  }

  return (
    <button onClick={handleSave}>
      Save Data
    </button>
  )
}
```

### 3. Sử dụng với Imperative API (ở bất kỳ đâu)

```tsx
import { modalAPI, showLoading, showSuccess, showError } from '@/components/modal'

// Trong utility function
export async function saveUserData(data: any) {
  const loadingId = showLoading({
    title: 'Saving...',
    message: 'Please wait while we save your data.'
  })

  try {
    await api.saveUser(data)
    modalAPI.closeModal(loadingId)
    showSuccess('Data saved successfully!')
  } catch (error) {
    modalAPI.closeModal(loadingId)
    showError('Failed to save data. Please try again.')
  }
}

// Trong event handler
document.getElementById('save-btn')?.addEventListener('click', async () => {
  await saveUserData(userData)
})

// Trong service class
class UserService {
  async deleteUser(id: string) {
    return new Promise((resolve) => {
      showDeleteConfirm(
        async () => {
          await api.deleteUser(id)
          showSuccess('User deleted successfully!')
          resolve(true)
        },
        'this user'
      )
    })
  }
}
```

## API Reference

### Imperative API (Recommended)

Sử dụng imperative API để gọi modal từ bất kỳ đâu:

```tsx
import { modalAPI, showLoading, showSuccess, showError } from '@/components/modal'

// Hoặc sử dụng individual functions
import {
  showLoading,
  showConfirm,
  showAlert,
  showSuccess,
  showError,
  showWarning,
  showDeleteConfirm,
  closeModal,
  closeAllModals,
  updateModal
} from '@/components/modal'
```

### useModal Hook (Legacy)

#### Loading Modal
```tsx
const loadingId = modal.showLoading({
  title?: string,           // Default: 'Loading...'
  message?: string,         // Default: 'Please wait...'
  showSpinner?: boolean,    // Default: true
  allowClose?: boolean      // Default: false
})
```

#### Confirm Modal
```tsx
const confirmId = modal.showConfirm({
  title?: string,           // Default: 'Confirm Action'
  description?: string,     // Default: 'Are you sure...'
  confirmText?: string,     // Default: 'Confirm'
  cancelText?: string,      // Default: 'Cancel'
  variant?: 'default' | 'destructive',
  onConfirm: () => void | Promise<void>,
  onCancel?: () => void
})
```

#### Alert Modal
```tsx
const alertId = modal.showAlert({
  title?: string,           // Default: 'Alert'
  description?: string,     // Default: 'This is an alert...'
  okText?: string,          // Default: 'OK'
  variant?: 'default' | 'destructive' | 'warning' | 'success',
  onOk?: () => void
})
```

#### Convenience Methods
```tsx
// Success alert
modal.showSuccess('Operation completed!')

// Error alert
modal.showError('Something went wrong!')

// Warning alert
modal.showWarning('Please review your input.')

// Delete confirmation
modal.showDeleteConfirm(
  async () => await deleteItem(),
  'this item'
)
```

#### Utility Methods
```tsx
// Close specific modal
modal.closeModal(modalId)

// Close all modals
modal.closeAllModals()

// Update modal content
modal.updateModal(modalId, {
  title: 'New Title',
  message: 'Updated message'
})
```

## Ví dụ sử dụng

### 1. Loading Modal với API Call
```tsx
const handleSubmit = async (data) => {
  const loadingId = modal.showLoading({
    title: 'Processing...',
    message: 'Submitting your form data.',
    allowClose: false
  })

  try {
    await submitForm(data)
    modal.closeModal(loadingId)
    modal.showSuccess('Form submitted successfully!')
  } catch (error) {
    modal.closeModal(loadingId)
    modal.showError('Failed to submit form.')
  }
}
```

### 2. Delete Confirmation
```tsx
const handleDelete = (itemId) => {
  modal.showDeleteConfirm(
    async () => {
      await deleteItem(itemId)
      // Refresh data
      refetch()
    },
    'this booking'
  )
}
```

### 3. Form Validation Warning
```tsx
const handleNext = () => {
  if (!isFormValid) {
    modal.showWarning(
      'Please fill in all required fields before proceeding.'
    )
    return
  }
  
  // Continue with next step
  goToNextStep()
}
```

### 4. Update Loading Progress
```tsx
const handleUpload = async (file) => {
  const loadingId = modal.showLoading({
    title: 'Uploading...',
    message: 'Preparing file for upload.'
  })

  // Update progress
  modal.updateModal(loadingId, {
    message: 'Uploading file... 50%'
  })

  // Continue upload...
  await uploadFile(file)
  
  modal.updateModal(loadingId, {
    message: 'Processing uploaded file...'
  })

  await processFile()
  
  modal.closeModal(loadingId)
  modal.showSuccess('File uploaded successfully!')
}
```

## Styling

Modal sử dụng Tailwind CSS và tương thích với dark mode. Các class chính:

- `fixed inset-0 z-50` - Fullscreen overlay
- `bg-black/50 backdrop-blur-sm` - Backdrop với blur effect
- `bg-white dark:bg-gray-900` - Modal background với dark mode
- `rounded-lg shadow-xl` - Rounded corners và shadow

## TypeScript Support

Tất cả các interface và type đều được export:

```tsx
import type {
  LoadingModalData,
  ConfirmModalData,
  AlertModalData,
  ModalData
} from '@/components/modal'
```
