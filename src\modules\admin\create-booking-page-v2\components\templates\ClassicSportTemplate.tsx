'use client'

import { cn } from '@/libs/utils'
import React from 'react'
import {
  BannerSection,
  BookingFormSection,
  ContactSection,
  DescriptionSection,
  MapSection,
} from './layouts'
import TableGridSlotSection from './layouts/TableGridSlotSection'

interface ClassicSportTemplateProps {
  className?: string
}

/**
 * Classic Sport Template Component
 *
 * Layout structure - Traditional vertical layout:
 * -----------------
 * |Banner image   |
 * -----------------
 * |description    |
 * -----------------
 * |booking info   |
 * -----------------
 * |grid slot      |
 * -----------------
 * |contact        |
 * -----------------
 * |map            |
 * -----------------
 */
const ClassicSportTemplate: React.FC<ClassicSportTemplateProps> = React.memo(({
  className,
}) => {
  const isMobile = false
  const isLive = false

  return (
    <div className={cn(
      'w-full max-w-[100rem] mx-auto bg-white',
      'px-4 md:px-0',
      className,
    )}
    >
      {/* Banner Section */}
      <BannerSection
        className="rounded-none"
      />

      {/* Description Section */}
      <DescriptionSection
        className="rounded-none mb-6"
      />

      <div
        className={cn(
          'gap-6 w-full mb-6',
          'grid-cols-1',
          'grid',
          'md:flex md:gap-6',
        )}
      >
        {/* Booking Form Section */}
        <div className="order-2 md:order-1 md:w-[400px] md:flex-shrink-0">
          <BookingFormSection />
        </div>
        {/* Grid Slot Section */}
        <div className="order-1 md:order-2 w-full md:flex-1">
          <TableGridSlotSection />
        </div>
      </div>

      <div className={cn(
        'grid gap-6',
        isLive ? 'grid-cols-1 md:grid-cols-2' : isMobile ? 'grid-cols-1' : 'grid-cols-2',
      )}
      >
        {/* Contact Section */}
        <ContactSection />

        {/* Map Section */}
        <MapSection />
      </div>
    </div>
  )
})

export default ClassicSportTemplate
