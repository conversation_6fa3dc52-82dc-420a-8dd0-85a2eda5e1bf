'use client'

import type { BaseLayoutProps } from './layouts/types'
import { cn } from '@/libs/utils'
import React from 'react'
import {
  BannerSection,
  BookingFormSection,
  ContactSection,
  DescriptionSection,
  MapSection,
} from './layouts'
import TableGridSlotSection from './layouts/TableGridSlotSection'

interface ClassicSportTemplateProps extends BaseLayoutProps {
  selectedDate?: Date
  onDateChange?: (date: Date) => void
  onSlotSelect?: (fieldId: string, timeSlot: string) => void
}

/**
 * Classic Sport Template Component
 *
 * Layout structure - Traditional vertical layout:
 * -----------------
 * |Banner image   |
 * -----------------
 * |description    |
 * -----------------
 * |booking info   |
 * -----------------
 * |grid slot      |
 * -----------------
 * |contact        |
 * -----------------
 * |map            |
 * -----------------
 */
const ClassicSportTemplate: React.FC<ClassicSportTemplateProps> = React.memo(({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  selectedDate,
  onDateChange,
  onSlotSelect,
}) => {
  const isMobile = previewMode === 'mobile'
  const isLive = previewMode === 'live'

  const onBookingSubmit = (data: {
    customerName: string
    customerEmail: string
    customerPhone: string
    notes?: string
  }) => {
    // eslint-disable-next-line no-console
    console.log(data)
  }

  return (
    <div className={cn(
      'w-full max-w-[100rem] mx-auto bg-white',
      isLive ? 'px-4 md:px-0' : isMobile ? 'p-4' : 'p-6',
      className,
    )}
    >
      {/* Banner Section */}
      <BannerSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        className="rounded-none"
      />

      {/* Description Section */}
      <DescriptionSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        className="rounded-none mb-6"
      />

      <div
        className={cn(
          'gap-6 w-full mb-6',
          'grid-cols-1',
          'grid',
          'md:flex md:gap-6',
        )}
      >
        {/* Booking Form Section */}
        <div className="order-2 md:order-1 md:w-[400px] md:flex-shrink-0">
          <BookingFormSection
            config={config}
            pageInfo={pageInfo}
            previewMode={previewMode}
            onSubmit={onBookingSubmit}
            showNotes={true}
          />
        </div>
        {/* Grid Slot Section */}
        <div className="order-1 md:order-2 w-full md:flex-1">
          <TableGridSlotSection
            config={config}
            pageInfo={pageInfo}
            previewMode={previewMode}
            selectedDate={selectedDate}
            onDateChange={onDateChange}
            onSlotSelect={onSlotSelect}
          />
        </div>
      </div>

      <div className={cn(
        'grid gap-6',
        isLive ? 'grid-cols-1 md:grid-cols-2' : isMobile ? 'grid-cols-1' : 'grid-cols-2',
      )}
      >
        {/* Contact Section */}
        <ContactSection
          config={config}
          pageInfo={pageInfo}
          previewMode={previewMode}
        />

        {/* Map Section */}
        <MapSection
          config={config}
          pageInfo={pageInfo}
          previewMode={previewMode}
          showDirections={config?.showDirections ?? true}
        />
      </div>
    </div>
  )
})

export default ClassicSportTemplate
