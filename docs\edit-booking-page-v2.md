# EditBookingPageV2Screen Documentation

## Overview

The `EditBookingPageV2Screen` is a new component that allows users to edit existing booking pages using the V2 interface. It provides a modern, step-by-step wizard interface for editing booking page configurations.

## Features

- **Data Loading**: Automatically fetches existing booking page data using `bookingPageAPIs.getBookingPageById()`
- **Data Mapping**: Converts BookingPageItem data structure to CreateBookingPageV2State format
- **Step-by-step Interface**: Uses the same 3-step wizard as the create flow
- **Error Handling**: Provides proper loading states and error messages
- **Auto-save**: Updates existing booking page instead of creating new one

## File Structure

```
src/modules/admin/create-booking-page-v2/
├── screens/
│   └── EditBookingPageV2Screen.tsx          # Main edit screen component
├── stores/
│   └── create-booking-page-v2.store.ts      # Updated store with initializeFromBookingPage method
├── types/
│   └── index.ts                             # Updated types with new method
└── components/
    └── ProgressHeader.tsx                   # Updated to support edit mode

src/app/[locale]/(auth)/admin/
└── edit-booking-page-v2/
    └── [id]/
        ├── page.tsx                         # Next.js page component
        └── loading.tsx                      # Loading component
```

## Usage

### Navigation

The edit screen can be accessed via:
- URL: `/admin/edit-booking-page-v2/{bookingPageId}`
- From manage booking pages: Click edit button on any booking page card

### Data Flow

1. **Initialization**: Component receives `bookingPageId` as prop
2. **Data Fetching**: Calls `bookingPageAPIs.getBookingPageById(bookingPageId)`
3. **Data Mapping**: Uses `initializeFromBookingPage()` to convert API response to store format
4. **Editing**: User can modify data through the 3-step wizard
5. **Saving**: Uses `bookingPageAPIs.updateBookingPage()` instead of create API

### Store Methods

#### `initializeFromBookingPage(bookingPageData: any)`

Converts BookingPageItem data structure to CreateBookingPageV2State format:

```typescript
// Maps API response to store format
const pageInfo: PageInfo = {
  name: bookingPageData.name || '',
  description: bookingPageData.description || '',
  slug: bookingPageData.slug || '',
}

const selectedTemplateId = bookingPageData.templateCode || ''

// Extracts config from first block
const configBlock = bookingPageData.blocks?.[0]
const configData = configBlock?.data || {}

const bookingConfig: BookingConfig = {
  banner: configData.banner || defaultBanner,
  openTime: configData.openTime || '06:00',
  closeTime: configData.closeTime || '22:00',
  fields: configData.fields || defaultFields,
  // ... other config properties
}
```

## Integration

### Route Configuration

Added to `src/utils/app-routes.ts`:

```typescript
admin: {
  // ... existing routes
  editBookingPageV2: (id: string) => `/admin/edit-booking-page-v2/${id}`,
}
```

### Updated ManageBookingPagesScreen

Modified the edit action to use V2 interface:

```typescript
const handleEditAction = (id: string) => {
  // Điều hướng đến trang edit V2 (giao diện mới)
  window.location.href = appPaths.admin.editBookingPageV2(id)
}
```

## Error Handling

The component includes comprehensive error handling:

- **Loading State**: Shows spinner while fetching data
- **Error State**: Displays error message with retry button
- **API Errors**: Shows toast notifications for API failures
- **Validation**: Uses same validation as create flow

## Benefits

1. **Consistent UX**: Same interface as create flow
2. **Modern Design**: Orange-themed gradient background
3. **Better Performance**: Optimized data loading and state management
4. **Maintainable**: Reuses existing V2 components and logic
5. **User Friendly**: Clear loading states and error messages

## Future Enhancements

- Add preview mode during editing
- Implement auto-save functionality
- Add change detection to warn about unsaved changes
- Support for bulk editing multiple booking pages
