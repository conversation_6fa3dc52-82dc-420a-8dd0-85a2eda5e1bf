'use client'

/* eslint-disable react-hooks-extra/no-direct-set-state-in-use-effect */
import { modalAPI } from '@/components/modal'
import { usePathname, useRouter } from '@/libs/i18nNavigation'
import { useCallback, useEffect, useState } from 'react'

export const useNavigation = () => {
  const router = useRouter()
  const pathname = usePathname()
  const [isNavigating, setIsNavigating] = useState(false)
  const [pendingPath, setPendingPath] = useState<string | null>(null)

  // Theo dõi thay đổi route để reset trạng thái navigating
  useEffect(() => {
    modalAPI.closeAllModals()

    if (pendingPath && pathname === pendingPath) {
      setIsNavigating(false)
      setPendingPath(null)
    }
  }, [pathname])

  const navigateBack = () => {
    router.back()
  }

  const navigate = async (path: string) => {
    // Nếu path là '#' hoặc path hiện tại giống với path đích (bỏ qua hash), không là<PERSON> gì cả
    const currentPathWithoutHash = pathname.split('#')[0]
    const targetPathWithoutHash = path === '#' ? pathname : path.split('#')[0]

    if (path === '#' || currentPathWithoutHash === targetPathWithoutHash) {
      router.push(path)
      return
    }

    let loadingId = ''
    try {
      loadingId = modalAPI.showLoading()
      setIsNavigating(true)
      setPendingPath(path)

      // Prefetch trước khi navigate
      await router.prefetch(path)

      // Navigate đến trang mới
      await router.push(path)

      // Nếu navigate thành công ngay lập tức, reset trạng thái
      if (pathname === path) {
        setIsNavigating(false)
        setPendingPath(null)
        modalAPI.closeModal(loadingId)
      }
    } catch (error) {
      console.error('Navigation error:', error)
      setIsNavigating(false)
      setPendingPath(null)
      modalAPI.closeModal(loadingId)
    } finally {
      setTimeout(() => {
        modalAPI.closeModal(loadingId)
      }, 4000)
    }
  }

  const navigateWithLoading = useCallback(async (path: string) => {
    if (isNavigating) {
      return
    } // Tránh navigate khi đang trong quá trình navigate

    try {
      setIsNavigating(true)
      setPendingPath(path)

      // Prefetch trước
      await router.prefetch(path)

      // Navigate
      await router.push(path)
    } catch (error) {
      console.error('Navigation error:', error)
      setIsNavigating(false)
      setPendingPath(null)
    }
  }, [router, isNavigating])

  return {
    navigate,
    navigateWithLoading,
    navigateBack,
    isNavigating,
    pendingPath,
  }
}
