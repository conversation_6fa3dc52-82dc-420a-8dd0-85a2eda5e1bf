'use client'

import type { BannerSectionProps } from './types'
import { cn } from '@/lib/utils'
import { useCreateBookingPageV2Store } from '@/modules/admin/create-booking-page-v2/stores/create-booking-page-v2.store'
import React from 'react'

const BannerSection: React.FC<BannerSectionProps> = ({
  className,
}) => {
  const banner = useCreateBookingPageV2Store(state => state.bookingConfig?.banner)

  return (
    <div className={cn(
      'relative w-full overflow-hidden rounded-lg',
      'h-48 md:h-64 lg:h-80',
      className,
    )}
    >
      <div
        className="absolute inset-0 bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${banner?.image || '/api/placeholder/800/400'})`,
          backgroundColor: banner?.backgroundColor || '#000',
          backgroundSize: banner?.imageFit === 'cover' ? '100% 100%' : 'contain',
        }}
      >
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center text-center text-white">
        <div className={cn(
          'max-w-4xl px-4',
          'space-y-2 md:space-y-4',
        )}
        >
          <h1 className={cn(
            'font-bold leading-tight',
            'text-xl md:text-3xl lg:text-5xl',
          )}
          >
            {banner?.title}
          </h1>
          <p className={cn(
            'text-gray-100',
            'text-sm md:text-lg lg:text-xl',
          )}
          >
            {banner?.subtitle}
          </p>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r" />
    </div>
  )
}

export default BannerSection
