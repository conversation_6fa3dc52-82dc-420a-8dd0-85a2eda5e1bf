# Project Sidebar Configuration

This directory contains the responsive and maintainable ProjectSidebar component that uses the modern SidebarProvider and SidebarTrigger components.

## Features

- ✅ **Responsive Design**: Works seamlessly on desktop and mobile
- ✅ **Collapsible**: Can be collapsed to icon-only mode
- ✅ **Keyboard Shortcuts**: Supports Ctrl/Cmd + B to toggle
- ✅ **Tooltips**: Shows tooltips when collapsed
- ✅ **Badge Support**: Display badges for notifications/counts
- ✅ **Easy Maintenance**: Simple configuration-based menu system
- ✅ **TypeScript**: Full type safety

## Components

### ProjectSidebar.tsx
The main sidebar component that renders the navigation menu using the modern sidebar components.

### menu-config.ts
Configuration file that defines all menu items and sections. This is where you add, remove, or modify menu items.

### ProjectDashboardLayout.tsx
The layout component that wraps the sidebar with proper responsive behavior.

## How to Add a New Menu Item

### 1. Simple Menu Item
```typescript
// In menu-config.ts
import { NewIcon } from 'lucide-react'

// Add to existing section
{
  icon: NewIcon,
  label: 'New Feature',
  url: '/admin/new-feature',
  isActive: false,
  description: 'Description for tooltip',
}
```

### 2. Menu Item with Badge
```typescript
{
  icon: NotificationIcon,
  label: 'Notifications',
  url: '/admin/notifications',
  isActive: false,
  badge: '5', // or badge: 5 for number
  description: 'View notifications',
}
```

### 3. New Menu Section
```typescript
// Add new section to projectMenuConfig array
{
  label: 'New Section',
  items: [
    {
      icon: SomeIcon,
      label: 'Item 1',
      url: '/admin/item1',
      isActive: false,
      description: 'First item description',
    },
    // ... more items
  ],
}
```

## Utility Functions

The menu-config.ts file provides utility functions for dynamic menu management:

```typescript
// Add menu item to existing section
addMenuItem(0, newMenuItem) // Add to first section

// Remove menu item
removeMenuItem(0, 'Item Label') // Remove from first section

// Update active state
updateMenuItemActive('Dashboard', true) // Set Dashboard as active

// Get current active item
const activeItem = getActiveMenuItem()
```

## Responsive Behavior

- **Desktop**: Sidebar can be collapsed to icon-only mode
- **Mobile**: Sidebar becomes a slide-out sheet overlay
- **Keyboard**: Ctrl/Cmd + B toggles sidebar
- **Touch**: Swipe gestures supported on mobile

## Styling

The sidebar uses consistent orange theming:
- Active items: `bg-orange-500 text-white`
- Hover states: `hover:text-orange-600 hover:bg-orange-50`
- Badges: `bg-orange-100 text-orange-600`

## Integration

The sidebar is integrated into the ProjectDashboardLayout and automatically:
- Manages state with cookies for persistence
- Provides SidebarTrigger in the header
- Handles responsive breakpoints
- Maintains proper z-index layering

## Example Usage

```tsx
// The sidebar is automatically included in ProjectDashboardLayout
<ProjectDashboardLayout>
  <YourPageContent />
</ProjectDashboardLayout>
```

The SidebarTrigger is automatically placed in the layout header and will toggle the sidebar state.
