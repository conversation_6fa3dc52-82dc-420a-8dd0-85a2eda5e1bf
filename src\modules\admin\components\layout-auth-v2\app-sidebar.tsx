'use client'

import type { BookingPageItem } from '../../apis/booking-page.api'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'
import { useEffect } from 'react'
import { useSetPickslotPage } from '../../pickslot-page/stores/booking-page.store'
import { sidebarData } from './data/sidebar-data'
import { NavGroup } from './nav-group'
import { NavUser } from './nav-user'
import { TeamSwitcher } from './team-switcher'

interface Props {
  bookingPageItem: BookingPageItem
}

export function AppSidebar({ bookingPageItem, ...props }: Props & React.ComponentProps<typeof Sidebar>) {
  const setBookingPage = useSetPickslotPage()

  useEffect(() => {
    if (bookingPageItem) {
      setBookingPage(bookingPageItem)
    }
  }, [bookingPageItem, setBookingPage])

  // Use booking page data if available, otherwise fallback to static data
  const pageData = sidebarData(bookingPageItem._id)

  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        <TeamSwitcher bookingPageItem={bookingPageItem} />
      </SidebarHeader>
      <SidebarContent>
        {pageData.navGroups.map(groupProps => (
          <NavGroup key={groupProps.title} {...groupProps} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
