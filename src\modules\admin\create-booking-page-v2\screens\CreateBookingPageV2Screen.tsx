'use client'

import React from 'react'
import { BookingConfigStep } from '../components'
import { ProgressHeader } from '../components/ProgressHeader'
import { PageInfoStep } from '../components/steps/PageInfoStep'
import { TemplateSelectionStep } from '../components/steps/TemplateSelectionStep'
import { useCreateBookingPageV2Store } from '../stores/create-booking-page-v2.store'

export const CreateBookingPageV2Screen: React.FC = () => {
  const currentStep = useCreateBookingPageV2Store(state => state.currentStep)

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <PageInfoStep />
      case 2:
        return <TemplateSelectionStep />
      case 3:
        return <BookingConfigStep />
      default:
        return <PageInfoStep />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-orange-100/30 to-orange-200/50 flex flex-col">
      {/* Progress Header */}
      <ProgressHeader />

      {/* Main Content */}
      <div className="container mx-auto py-6 lg:py-6 px-4">
        <div className="transition-all duration-300">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  )
}

export default CreateBookingPageV2Screen
