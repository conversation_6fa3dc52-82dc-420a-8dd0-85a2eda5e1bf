import { format } from 'date-fns'

export const formatDateYMD = (date: Date | string) => {
  try {
    return format(date, 'yyyy-MM-dd')
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('error formatDateYMD', date, error)
    return ''
  }
}

export const formatDateYMDHMS = (date: Date) => {
  return date.toLocaleDateString('vi-VN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}
